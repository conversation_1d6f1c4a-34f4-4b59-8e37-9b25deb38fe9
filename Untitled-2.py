def llm_response(message: str, nerfreal: BaseReal, conversation: ConversationHistory):
    start = time.perf_counter()
    
    # 添加系统提示词（只在对话开始时）
    if not conversation.messages:
        conversation.messages.append({
            'role': 'system', 
            'content': 'You are a helpful assistant. Respond concisely and naturally.'
        })
    
    # 添加用户新消息
    conversation.messages.append({'role': 'user', 'content': message})
    
    # 保持对话历史不超过限制
    if len(conversation.messages) > conversation.max_history * 2 + 1:  # 系统消息+多轮对话
        conversation.messages = [conversation.messages[0]] + conversation.messages[-(conversation.max_history * 2):]

    from openai import OpenAI
    client = OpenAI(
        api_key=os.getenv("DASHSCOPE_API_KEY"),
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    )

    result = ""
    first = True
    try:
        completion = client.chat.completions.create(
            model="qwen-plus",
            messages=conversation.messages,
            stream=True,
            stream_options={"include_usage": True}
        )

        for chunk in completion:
            if len(chunk.choices) > 0:
                if first:
                    end = time.perf_counter()
                    logger.info(f"llm Time to first chunk: {end-start}s")
                    first = False
                
                msg = chunk.choices[0].delta.content or ""
                lastpos = 0
                
                # 处理标点符号分段
                for i, char in enumerate(msg):
                    if char in ",.!;:，。！？：；":
                        result = result + msg[lastpos:i+1]
                        lastpos = i+1
                        if len(result) > 10:
                            logger.info(result)
                            nerfreal.put_msg_txt(result)
                            result = ""
                
                result = result + msg[lastpos:]

        # 将完整回复添加到对话历史
        full_response = result
        conversation.messages.append({'role': 'assistant', 'content': full_response})
        
        # 发送剩余文本
        if result:
            nerfreal.put_msg_txt(result)
            
        end = time.perf_counter()
        logger.info(f"llm Time to last chunk: {end-start}s")

    except Exception as e:
        logger.error(f"LLM API error: {str(e)}")
        nerfreal.put_msg_txt("抱歉，我遇到了一些技术问题，请稍后再试。")