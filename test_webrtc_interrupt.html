<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebRTC ASR 打断功能测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            text-align: center;
            color: #4a5568;
            margin-bottom: 30px;
        }
        
        .demo-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            background: #f7fafc;
        }
        
        .button-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin: 15px 0;
            align-items: center;
        }
        
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
            font-size: 14px;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ff4444, #cc0000);
            color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .btn-danger:hover {
            background: linear-gradient(135deg, #ff6666, #dd1111);
            transform: translateY(-2px);
        }
        
        .btn-danger:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #63b3ed, #4299e1);
            transform: translateY(-1px);
        }
        
        .status-display {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
            border: 2px solid #ccc;
            border-radius: 8px;
            font-weight: bold;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .shortcut-hint {
            position: fixed;
            top: 80px;
            right: 20px;
            padding: 8px 12px;
            background: rgba(0,0,0,0.8);
            color: white;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 999;
            opacity: 0.8;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .feature-list li:before {
            content: "✅ ";
            color: #48bb78;
            font-weight: bold;
        }
        
        textarea {
            width: 100%;
            padding: 10px;
            border: 2px solid #e2e8f0;
            border-radius: 5px;
            font-family: inherit;
            resize: vertical;
        }
        
        kbd {
            background: #f1f1f1;
            border: 1px solid #ccc;
            border-radius: 3px;
            padding: 2px 4px;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 WebRTC ASR 打断功能演示</h1>
        
        <div class="demo-section">
            <h3>📋 功能特点</h3>
            <ul class="feature-list">
                <li>专用打断按钮，响应速度快（50-200ms）</li>
                <li>多种快捷键支持：ESC / Ctrl+C / 空格键</li>
                <li>文本命令打断：输入"停止"、"打断"等</li>
                <li>实时状态反馈和视觉提示</li>
                <li>防重复点击保护</li>
                <li>双重备用机制确保可靠性</li>
            </ul>
        </div>
        
        <div class="demo-section">
            <h3>🎮 打断控制</h3>
            <div class="button-group">
                <button class="btn btn-danger" id="btn_interrupt" onclick="interruptAI()">🛑 立即打断</button>
                <span style="margin-left: 10px; font-size: 12px; color: #666;">
                    快捷键: <kbd>ESC</kbd> / <kbd>Ctrl+C</kbd> / <kbd>空格</kbd>
                </span>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>📝 文本输入测试</h3>
            <form id="echo-form">
                <div style="margin-bottom: 10px;">
                    <textarea id="message" rows="3" placeholder="输入消息...试试输入'停止'或'打断'"></textarea>
                </div>
                <button type="submit" class="btn btn-primary">发送消息</button>
            </form>
        </div>
        
        <div class="demo-section">
            <h3>🎥 录制控制</h3>
            <div class="button-group">
                <button class="btn btn-primary" id="btn_start_record">📹 开始录制</button>
                <button class="btn btn-primary" id="btn_stop_record" disabled>⏹️ 停止录制</button>
            </div>
        </div>
    </div>
    
    <!-- 状态显示 -->
    <div id="status" class="status-display">🟢 就绪</div>
    
    <!-- 快捷键提示 -->
    <div id="shortcutHint" class="shortcut-hint">
        <div style="font-size: 11px; margin-bottom: 3px;">快捷键打断：</div>
        <div style="font-size: 10px;">ESC / Ctrl+C / 空格键</div>
    </div>
    
    <input type="hidden" id="sessionid" value="123456">
    
    <script>
        // 更新状态显示
        function updateStatus(text, color, bgColor, borderColor) {
            const statusElement = document.getElementById('status');
            if (statusElement) {
                statusElement.textContent = text;
                statusElement.style.color = color || 'black';
                if (bgColor) {
                    statusElement.style.background = bgColor;
                }
                if (borderColor) {
                    statusElement.style.borderColor = borderColor;
                }
            }
        }

        // 增强的打断AI函数
        async function interruptAI() {
            console.log('🛑 用户触发打断操作');
            
            const statusElement = document.getElementById('status');
            const interruptButton = document.getElementById('btn_interrupt');
            
            // 立即显示打断状态
            updateStatus('🔴 正在打断...', 'red', 'linear-gradient(135deg, #ffe0e0, #ffcccc)', '#ff6666');
            
            // 禁用打断按钮，防止重复点击
            if (interruptButton) {
                interruptButton.disabled = true;
                interruptButton.style.opacity = '0.6';
                interruptButton.innerHTML = '⏳ 打断中...';
            }
            
            try {
                // 模拟API调用
                console.log('📡 发送打断请求...');
                await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 100));
                
                // 模拟90%成功率
                const success = Math.random() > 0.1;
                
                if (success) {
                    console.log('✅ 打断请求成功');
                    updateStatus('✅ 打断成功', 'green', 'linear-gradient(135deg, #e0ffe0, #ccffcc)', '#66cc66');
                    
                    setTimeout(() => {
                        updateStatus('🟢 就绪', 'black', 'linear-gradient(135deg, #f0f0f0, #e0e0e0)', '#ccc');
                    }, 2000);
                } else {
                    console.log('❌ 打断请求失败');
                    updateStatus('❌ 打断失败', 'red', 'linear-gradient(135deg, #ffe0e0, #ffcccc)', '#ff6666');
                }
                
            } catch (error) {
                console.error('⚠️ 打断请求出错:', error);
                updateStatus('⚠️ 打断错误', 'red', 'linear-gradient(135deg, #ffe0e0, #ffcccc)', '#ff6666');
            }
            
            // 重新启用打断按钮
            if (interruptButton) {
                interruptButton.disabled = false;
                interruptButton.style.opacity = '1';
                interruptButton.innerHTML = '🛑 立即打断';
            }
        }

        // 键盘快捷键支持
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape' || (event.ctrlKey && event.key === 'c')) {
                event.preventDefault();
                console.log('⌨️ 快捷键打断: ' + (event.key === 'Escape' ? 'ESC' : 'Ctrl+C'));
                interruptAI();
            }
            
            if (event.key === ' ' && event.target.tagName !== 'INPUT' && event.target.tagName !== 'TEXTAREA') {
                event.preventDefault();
                console.log('⌨️ 空格键打断');
                interruptAI();
            }
        });

        // 表单提交处理
        document.getElementById('echo-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const message = document.getElementById('message').value;
            
            if (message.includes('停止') || message.includes('打断') || message.includes('别说了') || message.toLowerCase().includes('stop')) {
                console.log('🛑 检测到文本打断命令: ' + message);
                interruptAI();
                document.getElementById('message').value = '';
                return;
            }
            
            console.log('📤 发送消息: ' + message);
            updateStatus('📤 发送中...', '#4299e1');
            
            // 模拟发送
            setTimeout(() => {
                updateStatus('✅ 消息已发送', 'green');
                setTimeout(() => {
                    updateStatus('🟢 就绪', 'black', 'linear-gradient(135deg, #f0f0f0, #e0e0e0)', '#ccc');
                }, 2000);
            }, 500);
            
            document.getElementById('message').value = '';
        });

        // 录制按钮处理
        document.getElementById('btn_start_record').addEventListener('click', function() {
            console.log('📹 开始录制');
            updateStatus('🔴 录制中...', 'red');
            this.disabled = true;
            document.getElementById('btn_stop_record').disabled = false;
        });

        document.getElementById('btn_stop_record').addEventListener('click', function() {
            console.log('⏹️ 停止录制');
            updateStatus('✅ 录制完成', 'green');
            this.disabled = true;
            document.getElementById('btn_start_record').disabled = false;
            
            setTimeout(() => {
                updateStatus('🟢 就绪', 'black', 'linear-gradient(135deg, #f0f0f0, #e0e0e0)', '#ccc');
            }, 2000);
        });

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 WebRTC ASR 打断功能演示页面已加载');
            
            // 5秒后淡化提示
            setTimeout(() => {
                const hint = document.getElementById('shortcutHint');
                if (hint) hint.style.opacity = '0.3';
            }, 5000);
        });
    </script>
</body>
</html>
