import time
import os
from basereal import BaseReal
from logger import logger
from typing import List, Dict


# # woody 全局变量存储对话历史
conversation_history: List[Dict[str, str]] = []
max_history = 5  # 最大对话历史数量

def add_message(role: str, content: str):
    """添加消息到对话历史"""
    global conversation_history
    conversation_history.append({"role": role, "content": content})
    
    # 保持对话历史不超过最大限制
    if len(conversation_history) > max_history * 2:  # 乘以2因为每次对话有user和assistant两条
        conversation_history = conversation_history[-max_history * 2:]



def get_response(user_input: str,  model: str = "qwen-plus") -> str:
    """获取AI的回复"""
    global conversation_history

    # 添加用户消息到历史
    add_message("user", user_input)

    from openai import OpenAI
    client = OpenAI(
        # 如果您没有配置环境变量，请在此处用您的API Key进行替换
        api_key=os.getenv("DASHSCOPE_API_KEY"),
        # 填写DashScope SDK的base_url
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    )
    
    try:
        # 设置API密钥
        # openai.api_key = api_key
        
        # 调用OpenAI API
        # response = openai.ChatCompletion.create(
        #     model=model,
        #     messages=conversation_history
        # )

        response = client.chat.completions.create(
        model=model,
        # model = "deepseek-r1:7b",
        messages=conversation_history
        stream=True,
        # 通过以下设置，在流式输出的最后一行展示token使用信息
        stream_options={"include_usage": True}
    )
        
        # 获取AI回复
        # ai_response = response['choices'][0]['message']['content']
        ai_response = response
        
        # 添加AI回复到历史
        add_message("assistant", ai_response)
        
        return ai_response
    except Exception as e:
        return f"发生错误: {str(e)}"

def reset_conversation():
    """重置对话历史"""
    global conversation_history
    conversation_history = []






def llm_response(message,nerfreal:BaseReal):
    # start = time.perf_counter()
    # from openai import OpenAI
    # client = OpenAI(
    #     # 如果您没有配置环境变量，请在此处用您的API Key进行替换
    #     api_key=os.getenv("DASHSCOPE_API_KEY"),
    #     # 填写DashScope SDK的base_url
    #     base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    # )
    # # client = OpenAI(
    # #     base_url="http://localhost:11434/v1",  # Ollama API 地址
    # #     api_key="ollama"
    # # )
    # end = time.perf_counter()
    # logger.info(f"llm Time init: {end-start}s")
    # completion = client.chat.completions.create(
    #     model="qwen-plus",
    #     # model = "deepseek-r1:7b",
    #     messages=[{'role': 'system', 'content': 'You are a helpful assistant.'},
    #               {'role': 'user', 'content': message}],
    #     stream=True,
    #     # 通过以下设置，在流式输出的最后一行展示token使用信息
    #     stream_options={"include_usage": True}
    # )
    completion = get_response(message)

    result=""
    first = True
    for chunk in completion:
        if len(chunk.choices)>0:
            #print(chunk.choices[0].delta.content)
            if first:
                end = time.perf_counter()
                logger.info(f"llm Time to first chunk: {end-start}s")
                first = False
            msg = chunk.choices[0].delta.content
            lastpos=0
            #msglist = re.split('[,.!;:，。！?]',msg)
            # msg = msg.split("<think>")[-1]
            # msg = msg.split("</think>")[-1]
            for i, char in enumerate(msg):
                if char in ",.!;:，。！？：；" :
                    result = result+msg[lastpos:i+1]
                    lastpos = i+1
                    if len(result)>10:
                        logger.info(result)
                        nerfreal.put_msg_txt(result)
                        result=""
            result = result+msg[lastpos:]
    end = time.perf_counter()
    logger.info(f"llm Time to last chunk: {end-start}s")
    nerfreal.put_msg_txt(result)    