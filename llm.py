import time
import os
import threading
from basereal import BaseReal
from logger import logger
from typing import List, Dict
# from app import ConversationHistory

from dataclasses import dataclass

@dataclass
class ConversationHistory:
    messages: List[Dict[str, str]]
    max_history: int = 5 #最大对话轮数
    def __init__(self, messages=None):
        self.messages = messages if messages is not None else []


# # woody 全局变量存储对话历史
# conversation_history: List[Dict[str, str]] = []
# max_history = 5  # 最大对话历史数量

# def add_message(role: str, content: str):
#     """添加消息到对话历史"""
#     global conversation_history
#     conversation_history.append({"role": role, "content": content})
    
#     # 保持对话历史不超过最大限制
#     if len(conversation_history) > max_history * 2:  # 乘以2因为每次对话有user和assistant两条
#         conversation_history = conversation_history[-max_history * 2:]



# def get_response(user_input: str,  model: str = "qwen-plus") -> str:
#     """获取AI的回复"""
#     global conversation_history

#     # 添加用户消息到历史
#     add_message("user", user_input)

#     from openai import OpenAI
#     client = OpenAI(
#         # 如果您没有配置环境变量，请在此处用您的API Key进行替换
#         api_key=os.getenv("DASHSCOPE_API_KEY"),
#         # 填写DashScope SDK的base_url
#         base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
#     )
    
#     try:
#         # 设置API密钥
#         # openai.api_key = api_key
        
#         # 调用OpenAI API
#         # response = openai.ChatCompletion.create(
#         #     model=model,
#         #     messages=conversation_history
#         # )

#         response = client.chat.completions.create(
#         model=model,
#         # model = "deepseek-r1:7b",
#         messages=conversation_history
#         stream=True,
#         # 通过以下设置，在流式输出的最后一行展示token使用信息
#         stream_options={"include_usage": True}
#     )
        
#         # 获取AI回复
#         # ai_response = response['choices'][0]['message']['content']
#         ai_response = response
        
#         # 添加AI回复到历史
#         add_message("assistant", ai_response)
        
#         return ai_response
#     except Exception as e:
#         return f"发生错误: {str(e)}"

# def reset_conversation():
#     """重置对话历史"""
#     global conversation_history
#     conversation_history = []

def llm_response(text, nerfreal, conversation):
    """生成LLM响应，确保在每个关键点检查打断标志"""
    # 在函数开始时检查打断标志
    if hasattr(nerfreal, 'interrupt_audio') and nerfreal.interrupt_audio:
        print(f"[LLM] 函数开始时检测到打断标志为True，跳过处理，时间：{time.strftime('%H:%M:%S')}")
        return ""
    
    # 记录开始时间
    start = time.perf_counter()
    
    # 添加用户消息到对话历史
    conversation.messages.append({'role': 'user', 'content': text})
    
    # 再次检查打断标志
    if hasattr(nerfreal, 'interrupt_audio') and nerfreal.interrupt_audio:
        print(f"[LLM] 添加用户消息后检测到打断标志为True，跳过处理，时间：{time.strftime('%H:%M:%S')}")
        return ""
    
    try:
        # 创建API客户端
        client = OpenAI(
            api_key=os.getenv("DASHSCOPE_API_KEY"),
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
        )
        
        # 每次发送请求前检查打断标志
        if hasattr(nerfreal, 'interrupt_audio') and nerfreal.interrupt_audio:
            print(f"[LLM] 发送请求前检测到打断标志为True，跳过处理，时间：{time.strftime('%H:%M:%S')}")
            return ""
        
        # 创建一个事件标志，用于在打断时取消请求
        interrupt_event = threading.Event()
        
        # 创建一个线程来监控打断标志
        def check_interrupt():
            while True:
                if hasattr(nerfreal, 'interrupt_audio') and nerfreal.interrupt_audio:
                    print(f"[LLM] 监控线程检测到打断标志为True，设置中断事件，时间：{time.strftime('%H:%M:%S')}")
                    interrupt_event.set()
                    break
                time.sleep(0.1)  # 每100ms检查一次
        
        # 启动监控线程
        monitor_thread = threading.Thread(target=check_interrupt, daemon=True)
        monitor_thread.start()
        
        result = ""
        first = True
        
        try:
            # 创建聊天完成请求
            completion = client.chat.completions.create(
                model="qwen-plus",
                messages=conversation.messages,
                stream=True
            )
            
            # 再次检查打断标志
            if hasattr(nerfreal, 'interrupt_audio') and nerfreal.interrupt_audio:
                print(f"[LLM] 创建聊天完成请求后检测到打断标志为True，跳过处理，时间：{time.strftime('%H:%M:%S')}")
                return ""
            
            # 处理流式响应
            for chunk in completion:
                # 在每个块开始时检查打断标志
                if hasattr(nerfreal, 'interrupt_audio') and nerfreal.interrupt_audio:
                    print(f"[LLM] 处理响应块前检测到打断标志为True，停止处理，时间：{time.strftime('%H:%M:%S')}")
                    return ""
                
                if len(chunk.choices) > 0:
                    if first:
                        end = time.perf_counter()
                        logger.info(f"llm time to first chunk: {end-start}s")
                        first = False
                    
                    msg = chunk.choices[0].delta.content or ""
                    lastpos = 0

                    # 处理标点符号分段
                    for i, char in enumerate(msg):
                        # 在每次循环开始时检查打断标志
                        if hasattr(nerfreal, 'interrupt_audio') and nerfreal.interrupt_audio:
                            print(f"[LLM] 处理文本块前检测到打断标志为True，停止处理，时间：{time.strftime('%H:%M:%S')}")
                            return ""  # 如果检测到打断，直接返回
                        
                        if char in ",.!;:，。！？：；":
                            result = result + msg[lastpos:i+1]
                            lastpos = i+1
                            if len(result) > 10:
                                logger.info(result)
                                # 再次检查打断状态
                                if hasattr(nerfreal, 'interrupt_audio') and nerfreal.interrupt_audio:
                                    print(f"[LLM] 发送文本块前检测到打断标志为True，停止处理，时间：{time.strftime('%H:%M:%S')}")
                                    return ""  # 如果检测到打断，直接返回
                                nerfreal.put_msg_txt(result)
                                result = ""
                    
                    result = result + msg[lastpos:]

                
            # 将完整回复添加到对话历史
            full_response = result
            conversation.messages.append(
                {
                    'role': 'assistant',
                    'content': full_response
                }
            )
            
            # 发送剩余文本
            if result:
                # 最后一次检查打断标志
                if hasattr(nerfreal, 'interrupt_audio') and nerfreal.interrupt_audio:
                    print(f"[LLM] 发送最后文本块前检测到打断标志为True，停止处理，时间：{time.strftime('%H:%M:%S')}")
                    return ""
                nerfreal.put_msg_txt(result)
            
            end = time.perf_counter()
            logger.info(f"llm time to last chunk: {end-start}s")
            
            return full_response
            
        except Exception as e:
            logger.error(f"LLM API error: {str(e)}")
            # 检查是否是由于打断导致的异常
            if hasattr(nerfreal, 'interrupt_audio') and nerfreal.interrupt_audio:
                print(f"[LLM] 异常处理中检测到打断标志为True，跳过错误处理，时间：{time.strftime('%H:%M:%S')}")
                return ""
            nerfreal.put_msg_txt("抱歉，我遇到了一些技术问题，请稍后再试。")
            return "抱歉，我遇到了一些技术问题，请稍后再试。"
