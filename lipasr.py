###############################################################################
#  Copyright (C) 2024 LiveTalking@lipku https://github.com/lipku/LiveTalking
#  email: <EMAIL>
# 
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#  
#       http://www.apache.org/licenses/LICENSE-2.0
# 
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
###############################################################################

import time
import torch
import numpy as np

import queue
from queue import Queue
#import multiprocessing as mp

from baseasr import BaseASR
from wav2lip import audio

class LipASR(BaseASR):

    def __init__(self, opt, parent):
        super().__init__(opt, parent)
        
        # 设备
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 关键词检测相关变量
        self.keyword_buffer = []
        self.keyword_buffer_size = 50  # 保存约1秒的音频用于检测
        self.keywords = ["停止", "暂停", "别说了", "打断"]  # 可配置的关键词列表
        
        # 确保我们有processor和model
        # 如果在父类中已经加载，则直接使用
        if hasattr(self, 'processor') and hasattr(self, 'model'):
            pass
        else:
            # 否则，加载默认的ASR模型
            from transformers import AutoProcessor, AutoModelForCTC
            
            print(f"[INFO] 为关键词检测加载ASR模型 {opt.asr_model}...")
            self.processor = AutoProcessor.from_pretrained(opt.asr_model)
            self.model = AutoModelForCTC.from_pretrained(opt.asr_model).to(self.device)

    def run_step(self):
        # 在方法开始时记录打断状态
        was_interrupted = False
        if hasattr(self.parent, 'interrupt_audio') and self.parent.interrupt_audio:
            was_interrupted = True
            print(f">>>>>>>>>>>>>>>>>进入打断状态！时间：{time.strftime('%H:%M:%S')}")
        
        # 检查是否有关键词触发打断
        # 这里可以添加检查最近的ASR结果是否包含"停止"、"打断"等关键词的逻辑
        recent_asr_result = self.get_recent_asr_result() if hasattr(self, 'get_recent_asr_result') else None
        if recent_asr_result and any(keyword in recent_asr_result for keyword in ["停止", "打断", "stop", "interrupt"]):
            print(f"ASR检测到关键词: {recent_asr_result}，时间：{time.strftime('%H:%M:%S')}")
            if hasattr(self.parent, 'interrupt_audio'):
                self.parent.interrupt_audio = True
                print(f"ASR设置打断标志为True，时间：{time.strftime('%H:%M:%S')}")
                # 调用flush_talk方法
                if hasattr(self.parent, 'flush_talk'):
                    self.parent.flush_talk()
                    print(f"ASR调用flush_talk方法，时间：{time.strftime('%H:%M:%S')}")
        
        for _ in range(self.batch_size*2):
            # woody4interrupt
            if hasattr(self.parent, 'interrupt_audio') and self.parent.interrupt_audio:
                if not was_interrupted:  # 只在状态变化时打印
                    print(f">>>>>>>>>>>>>>>>>进入打断状态！时间：{time.strftime('%H:%M:%S')}")
                frame = np.zeros(self.chunk, dtype=np.float32)
                type = 1
                eventpoint = None
            else:
                if was_interrupted:  # 只在状态变化时打印
                    print(f">>>>>>>>>>>>>>>>>退出打断状态！时间：{time.strftime('%H:%M:%S')}")
                frame,type,eventpoint = self.get_audio_frame()
            self.frames.append(frame)
            # put to output
            self.output_queue.put((frame,type,eventpoint))
        # context not enough, do not run network.
        if len(self.frames) <= self.stride_left_size + self.stride_right_size:
            return
        
        inputs = np.concatenate(self.frames) # [N * chunk]
        mel = audio.melspectrogram(inputs)
        #print(mel.shape[0],mel.shape,len(mel[0]),len(self.frames))
        # cut off stride
        left = max(0, self.stride_left_size*80/50)
        right = min(len(mel[0]), len(mel[0]) - self.stride_right_size*80/50)
        mel_idx_multiplier = 80.*2/self.fps 
        mel_step_size = 16
        i = 0
        mel_chunks = []
        while i < (len(self.frames)-self.stride_left_size-self.stride_right_size)/2:
            start_idx = int(left + i * mel_idx_multiplier)
            #print(start_idx)
            if start_idx + mel_step_size > len(mel[0]):
                mel_chunks.append(mel[:, len(mel[0]) - mel_step_size:])
            else:
                mel_chunks.append(mel[:, start_idx : start_idx + mel_step_size])
            i += 1
        self.feat_queue.put(mel_chunks)
        
        # discard the old part to save memory
        self.frames = self.frames[-(self.stride_left_size + self.stride_right_size):]

    def detect_keyword(self, frame):
        """检测音频中是否包含关键词"""
        # 将当前帧添加到关键词缓冲区
        self.keyword_buffer.append(frame)
        
        # 保持缓冲区大小
        if len(self.keyword_buffer) > self.keyword_buffer_size:
            self.keyword_buffer = self.keyword_buffer[-self.keyword_buffer_size:]
            
            # 每当缓冲区填满时进行一次关键词检测
            buffer_audio = np.concatenate(self.keyword_buffer)
            
            # 使用快速ASR识别函数进行文本识别
            text = self.quick_asr_recognize(buffer_audio)
            
            # 检查识别文本中是否包含关键词
            if text and any(keyword in text for keyword in self.keywords):
                print(f"检测到关键词: {text}")
                # 触发打断
                if hasattr(self.parent, 'interrupt_audio'):
                    self.parent.interrupt_audio = True
                    # 添加以下代码来清空TTS队列
                    if hasattr(self.parent, 'tts') and hasattr(self.parent.tts, 'msgqueue'):
                        print(">>>>>>>>>>清空TTS消息队列")
                        while not self.parent.tts.msgqueue.empty():
                            try:
                                self.parent.tts.msgqueue.get_nowait()
                            except:
                                pass
                else:
                    # 如果parent没有interrupt_audio属性，则调用flush_talk方法
                    self.parent.flush_talk()
                    
                # 清空缓冲区
                self.keyword_buffer = []
                
                # 返回True表示检测到关键词
                return True
                
        return False

    def quick_asr_recognize(self, audio_buffer):
        """
        使用默认的wav2vec2-large-xlsr-53-esperanto模型进行快速ASR识别
        
        参数:
            audio_buffer: numpy数组，包含音频样本
        
        返回:
            识别出的文本
        """
        try:
            # 确保音频长度足够
            if len(audio_buffer) < 1000:  # 太短的音频可能无法识别
                return ""
            
            # 使用已有的processor处理音频
            inputs = self.processor(
                audio_buffer, 
                sampling_rate=self.sample_rate, 
                return_tensors="pt", 
                padding=True
            )
            
            # 将输入移到正确的设备上
            input_values = inputs.input_values.to(self.device)
            
            # 使用no_grad以提高速度
            with torch.no_grad():
                # 获取模型输出
                outputs = self.model(input_values)
                logits = outputs.logits
                
                # 获取最可能的token序列
                predicted_ids = torch.argmax(logits, dim=-1)
                
                # 解码为文本
                transcription = self.processor.batch_decode(predicted_ids)[0].lower()
            
            return transcription
        
        except Exception as e:
            print(f"快速ASR识别错误: {e}")
            return ""

    def process_audio_frame(self, frame):
        # 现有的音频处理代码...
        
        # 添加关键词检测
        if hasattr(self, 'detect_keywords') and callable(self.detect_keywords):
            text = self.frame_to_text(frame)
            if text and any(keyword in text for keyword in ["停止", "打断", "stop", "interrupt"]):
                print(f"ASR检测到关键词: {text}")
                if hasattr(self.parent, 'interrupt_audio'):
                    self.parent.interrupt_audio = True
                    print("ASR设置打断标志为True")
                    # 调用flush_talk方法
                    if hasattr(self.parent, 'flush_talk'):
                        self.parent.flush_talk()
                        print("ASR调用flush_talk方法")

    def detect_keywords(self, text):
        """检测文本中是否包含关键词，如果包含则触发打断"""
        # 将文本添加到缓冲区
        self.keyword_buffer.append(text)
        
        # 保持缓冲区大小不超过限制
        if len(self.keyword_buffer) > self.keyword_buffer_size:
            self.keyword_buffer.pop(0)
        
        # 检查缓冲区中是否包含关键词
        combined_text = " ".join(self.keyword_buffer)
        
        # 检查识别文本中是否包含关键词
        if text and any(keyword in text for keyword in self.keywords):
            print(f"检测到关键词: {text}，时间：{time.strftime('%H:%M:%S')}")
            
            # 触发立即打断
            if hasattr(self.parent, 'immediate_stop_audio'):
                try:
                    # 使用新的立即停止方法
                    self.parent.immediate_stop_audio()
                    print(f"ASR调用immediate_stop_audio方法，时间：{time.strftime('%H:%M:%S')}")
                except Exception as e:
                    print(f"ASR调用immediate_stop_audio方法时出错：{str(e)}，时间：{time.strftime('%H:%M:%S')}")
            elif hasattr(self.parent, 'interrupt_audio'):
                # 回退到原有的打断方法
                self.parent.interrupt_audio = True
                print(f"ASR设置打断标志为True，时间：{time.strftime('%H:%M:%S')}")

                # 调用flush_talk方法
                if hasattr(self.parent, 'flush_talk'):
                    try:
                        self.parent.flush_talk()
                        print(f"ASR调用flush_talk方法，时间：{time.strftime('%H:%M:%S')}")
                    except Exception as e:
                        print(f"ASR调用flush_talk方法时出错：{str(e)}，时间：{time.strftime('%H:%M:%S')}")
            else:
                # 如果parent没有相关方法，则尝试调用flush_talk方法
                try:
                    if hasattr(self.parent, 'flush_talk'):
                        self.parent.flush_talk()
                        print(f"ASR调用flush_talk方法（无interrupt_audio属性），时间：{time.strftime('%H:%M:%S')}")
                except Exception as e:
                    print(f"ASR调用flush_talk方法时出错：{str(e)}，时间：{time.strftime('%H:%M:%S')}")
            
            # 清空缓冲区
            self.keyword_buffer = []
            
            # 返回True表示检测到关键词
            return True
        
        return False
