###############################################################################
#  Copyright (C) 2024 LiveTalking@lipku https://github.com/lipku/LiveTalking
#  email: <EMAIL>
# 
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#  
#       http://www.apache.org/licenses/LICENSE-2.0
# 
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
###############################################################################
from __future__ import annotations
import time
import numpy as np
import soundfile as sf
import resampy
import asyncio
import edge_tts

import os
import hmac
import hashlib
import base64
import json
import uuid

from typing import Iterator

import requests

import queue
from queue import Queue
from io import BytesIO
from threading import Thread, Event
from enum import Enum

from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from basereal import BaseReal

from logger import logger
class State(Enum):
    RUNNING=0
    PAUSE=1

class BaseTTS:
    def __init__(self, opt, parent:BaseReal):
        self.opt=opt
        self.parent = parent

        self.fps = opt.fps # 20 ms per frame
        self.sample_rate = 16000
        self.chunk = self.sample_rate // self.fps # 320 samples per chunk (20ms * 16000 / 1000)
        self.input_stream = BytesIO()

        self.msgqueue = Queue()
        self.state = State.RUNNING

    def flush_talk(self):
        self.msgqueue.queue.clear()
        self.state = State.PAUSE

    def put_msg_txt(self,msg:str,eventpoint=None): 
        if len(msg)>0:
            self.msgqueue.put((msg,eventpoint))

    def process_tts(self, quit_event):
        """处理TTS消息，确保在每次处理消息前和处理过程中都检查打断标志"""
        while not quit_event.is_set():
            # 检查打断标志（队列为空时）
            if hasattr(self.parent, 'interrupt_audio') and self.parent.interrupt_audio:
                print(f"[TTS] 队列为空时检测到打断标志为True，等待新消息，时间：{time.strftime('%H:%M:%S')}")
                
                # 清空队列
                try:
                    while not self.msgqueue.empty():
                        try:
                            self.msgqueue.get_nowait()
                        except:
                            pass
                    print(f"[TTS] 已清空消息队列，时间：{time.strftime('%H:%M:%S')}")
                except Exception as e:
                    print(f"[TTS] 清空消息队列时出错：{str(e)}，时间：{time.strftime('%H:%M:%S')}")
                
                time.sleep(0.1)  # 短暂休眠，避免CPU占用过高
                continue
            
            try:
                # 尝试从队列获取消息
                msg = self.msgqueue.get(block=True, timeout=0.1)
                
                # 获取消息后再次检查打断标志
                if hasattr(self.parent, 'interrupt_audio') and self.parent.interrupt_audio:
                    print(f"[TTS] 获取消息后检测到打断标志为True，跳过处理，时间：{time.strftime('%H:%M:%S')}")
                    continue
                
                # 设置状态为运行中
                self.state = State.RUNNING
                
                # 再次检查打断标志（开始处理前）
                if hasattr(self.parent, 'interrupt_audio') and self.parent.interrupt_audio:
                    print(f"[TTS] 开始处理前检测到打断标志为True，跳过处理，时间：{time.strftime('%H:%M:%S')}")
                    continue
                
                # 处理消息
                print(f"[TTS] 开始处理消息：{msg[0][:30]}...，时间：{time.strftime('%H:%M:%S')}")
                self.txt_to_audio(msg)
                
                # 处理完成后检查打断标志
                if hasattr(self.parent, 'interrupt_audio') and self.parent.interrupt_audio:
                    print(f"[TTS] 处理完成后检测到打断标志为True，清空队列，时间：{time.strftime('%H:%M:%S')}")
                    # 清空消息队列
                    while not self.msgqueue.empty():
                        try:
                            self.msgqueue.get_nowait()
                        except:
                            pass
            except queue.Empty:
                # 队列为空，继续循环
                pass

    def txt_to_audio(self, msg):
        """处理文本到音频的转换，确保在处理过程中检查打断标志"""
        # 在方法开始时检查打断标志
        if hasattr(self.parent, 'interrupt_audio') and self.parent.interrupt_audio:
            print(f"[TTS] txt_to_audio开始时检测到打断标志为True，跳过处理，时间：{time.strftime('%H:%M:%S')}")
            return
        
        # 记录开始时间
        start_time = time.time()
        
        # 提取文本和事件
        text, textevent = msg
        
        # 记录处理信息
        print(f"[TTS] 开始处理文本：{text[:30]}...，时间：{time.strftime('%H:%M:%S')}")
        
        # 在关键处理点检查打断标志
        if hasattr(self.parent, 'interrupt_audio') and self.parent.interrupt_audio:
            print(f"[TTS] txt_to_audio中间检测到打断标志为True，跳过处理，时间：{time.strftime('%H:%M:%S')}")
            return
        
        # 调用子类实现的具体转换逻辑
        # 注意：子类应该在处理过程中定期检查打断标志
        
        # 记录结束时间
        end_time = time.time()
        print(f"[TTS] 处理完成，耗时：{end_time-start_time:.2f}秒，时间：{time.strftime('%H:%M:%S')}")

    def stop_current_audio(self):
        """立即停止当前正在播放的音频"""
        print("停止当前正在播放的音频")
        
        # 设置状态为PAUSE，这会导致txt_to_audio中的循环退出
        self.state = State.PAUSE
        
        # 清空输入流缓冲区
        if hasattr(self, 'input_stream'):
            self.input_stream.seek(0)
            self.input_stream.truncate()
        
        # 如果有其他缓冲区或状态需要重置，也在这里处理
        # 例如，对于EdgeTTS类，可能需要中断communicate.stream()
        
        # 通知父对象停止音频播放
        if hasattr(self.parent, 'flush_talk'):
            self.parent.flush_talk()
    
    def txt_to_audio(self, msg):
        if hasattr(self.parent, 'interrupt_audio') and self.parent.interrupt_audio:
            print(">>>>>>>>>>TTS处理过程中检测到打断")
            return
        pass
    

###########################################################################################
class EdgeTTS(BaseTTS):
    def txt_to_audio(self,msg):
        voicename = "zh-CN-YunxiaNeural"
        text,textevent = msg
        t = time.time()
        asyncio.new_event_loop().run_until_complete(self.__main(voicename,text))
        logger.info(f'-------edge tts time:{time.time()-t:.4f}s')
        if self.input_stream.getbuffer().nbytes<=0: #edgetts err
            logger.error('edgetts err!!!!!')
            return
        
        self.input_stream.seek(0)
        stream = self.__create_bytes_stream(self.input_stream)
        streamlen = stream.shape[0]
        idx=0
        # 使用更小的chunk来提高响应速度（10ms而不是20ms）
        mini_chunk = self.chunk // 2  # 160 samples = 10ms
        first = True

        while streamlen >= mini_chunk and self.state==State.RUNNING:
            # 在每个小片段处理前都检查打断标志
            if hasattr(self.parent, 'interrupt_audio') and self.parent.interrupt_audio:
                print(">>>>>>>>>>EdgeTTS流处理过程中检测到打断（小片段级别）")
                break

            eventpoint=None
            if first:
                eventpoint={'status':'start','text':text,'msgevent':textevent}
                first = False
            elif streamlen < mini_chunk * 2:  # 接近结束
                eventpoint={'status':'end','text':text,'msgevent':textevent}

            self.parent.put_audio_frame(stream[idx:idx+mini_chunk],eventpoint)
            streamlen -= mini_chunk
            idx += mini_chunk

        # 处理剩余的音频数据（如果有的话）
        if streamlen > 0 and self.state==State.RUNNING:
            if not (hasattr(self.parent, 'interrupt_audio') and self.parent.interrupt_audio):
                # 用零填充到mini_chunk大小
                remaining = np.zeros(mini_chunk, dtype=np.float32)
                remaining[:streamlen] = stream[idx:idx+streamlen]
                eventpoint={'status':'end','text':text,'msgevent':textevent}
                self.parent.put_audio_frame(remaining, eventpoint)

        self.input_stream.seek(0)
        self.input_stream.truncate()

    def __create_bytes_stream(self,byte_stream):
        #byte_stream=BytesIO(buffer)
        stream, sample_rate = sf.read(byte_stream) # [T*sample_rate,] float64
        logger.info(f'[INFO]tts audio stream {sample_rate}: {stream.shape}')
        stream = stream.astype(np.float32)

        if stream.ndim > 1:
            logger.info(f'[WARN] audio has {stream.shape[1]} channels, only use the first.')
            stream = stream[:, 0]
    
        if sample_rate != self.sample_rate and stream.shape[0]>0:
            logger.info(f'[WARN] audio sample rate is {sample_rate}, resampling into {self.sample_rate}.')
            stream = resampy.resample(x=stream, sr_orig=sample_rate, sr_new=self.sample_rate)

        return stream
    
    async def __main(self,voicename: str, text: str):
        try:
            communicate = edge_tts.Communicate(text, voicename)

            #with open(OUTPUT_FILE, "wb") as file:
            first = True
            async for chunk in communicate.stream():
                # 检查中断状态
                if hasattr(self.parent, 'interrupt_audio') and self.parent.interrupt_audio:
                    print(">>>>>>>>>>EdgeTTS流处理过程中检测到打断")
                    return
                
                if first:
                    first = False
                if chunk["type"] == "audio" and self.state==State.RUNNING:
                    #self.push_audio(chunk["data"])
                    self.input_stream.write(chunk["data"])
                    #file.write(chunk["data"])
                elif chunk["type"] == "WordBoundary":
                    pass
        except Exception as e:
            logger.exception('edgetts')

###########################################################################################
class FishTTS(BaseTTS):
    def txt_to_audio(self,msg): 
        text,textevent = msg
        self.stream_tts(
            self.fish_speech(
                text,
                self.opt.REF_FILE,  
                self.opt.REF_TEXT,
                "zh", #en args.language,
                self.opt.TTS_SERVER, #"http://127.0.0.1:5000", #args.server_url,
            ),
            msg
        )

    def fish_speech(self, text, reffile, reftext,language, server_url) -> Iterator[bytes]:
        start = time.perf_counter()
        req={
            'text':text,
            'reference_id':reffile,
            'format':'wav',
            'streaming':True,
            'use_memory_cache':'on'
        }
        try:
            res = requests.post(
                f"{server_url}/v1/tts",
                json=req,
                stream=True,
                headers={
                    "content-type": "application/json",
                },
            )
            end = time.perf_counter()
            logger.info(f"fish_speech Time to make POST: {end-start}s")

            if res.status_code != 200:
                logger.error("Error:%s", res.text)
                return
                
            first = True
        
            for chunk in res.iter_content(chunk_size=17640): # 1764 44100*20ms*2
                #print('chunk len:',len(chunk))
                if first:
                    end = time.perf_counter()
                    logger.info(f"fish_speech Time to first chunk: {end-start}s")
                    first = False
                if chunk and self.state==State.RUNNING:
                    yield chunk
            #print("gpt_sovits response.elapsed:", res.elapsed)
        except Exception as e:
            logger.exception('fishtts')

    def stream_tts(self,audio_stream,msg):
        text,textevent = msg
        first = True
        # 使用更小的chunk来提高响应速度（10ms而不是20ms）
        mini_chunk = self.chunk // 2  # 160 samples = 10ms

        for chunk in audio_stream:
            # 检查中断状态
            if hasattr(self.parent, 'interrupt_audio') and self.parent.interrupt_audio:
                print(">>>>>>>>>>TTS流处理过程中检测到打断")
                return

            if chunk is not None and len(chunk)>0:
                stream = np.frombuffer(chunk, dtype=np.int16).astype(np.float32) / 32767
                stream = resampy.resample(x=stream, sr_orig=44100, sr_new=self.sample_rate)
                streamlen = stream.shape[0]
                idx=0

                # 使用更小的chunk进行处理，每次处理都检查打断标志
                while streamlen >= mini_chunk:
                    # 在每个小片段处理前都检查打断标志
                    if hasattr(self.parent, 'interrupt_audio') and self.parent.interrupt_audio:
                        print(">>>>>>>>>>TTS流处理过程中检测到打断（小片段级别）")
                        return

                    eventpoint=None
                    if first:
                        eventpoint={'status':'start','text':text,'msgevent':textevent}
                        first = False

                    # 发送更小的音频片段
                    self.parent.put_audio_frame(stream[idx:idx+mini_chunk],eventpoint)
                    streamlen -= mini_chunk
                    idx += mini_chunk

                # 处理剩余的音频数据（如果有的话）
                if streamlen > 0:
                    if hasattr(self.parent, 'interrupt_audio') and self.parent.interrupt_audio:
                        return
                    # 用零填充到mini_chunk大小
                    remaining = np.zeros(mini_chunk, dtype=np.float32)
                    remaining[:streamlen] = stream[idx:idx+streamlen]
                    self.parent.put_audio_frame(remaining, None)

        eventpoint={'status':'end','text':text,'msgevent':textevent}
        self.parent.put_audio_frame(np.zeros(self.chunk,np.float32),eventpoint)

###########################################################################################
class SovitsTTS(BaseTTS):
    def txt_to_audio(self,msg): 
        text,textevent = msg
        self.stream_tts(
            self.gpt_sovits(
                text,
                self.opt.REF_FILE,  
                self.opt.REF_TEXT,
                "zh", #en args.language,
                self.opt.TTS_SERVER, #"http://127.0.0.1:5000", #args.server_url,
            ),
            msg
        )

    def gpt_sovits(self, text, reffile, reftext,language, server_url) -> Iterator[bytes]:
        start = time.perf_counter()
        req={
            'text':text,
            'text_lang':language,
            'ref_audio_path':reffile,
            'prompt_text':reftext,
            'prompt_lang':language,
            'media_type':'ogg',
            'streaming_mode':True
        }
        # req["text"] = text
        # req["text_language"] = language
        # req["character"] = character
        # req["emotion"] = emotion
        # #req["stream_chunk_size"] = stream_chunk_size  # you can reduce it to get faster response, but degrade quality
        # req["streaming_mode"] = True
        try:
            res = requests.post(
                f"{server_url}/tts",
                json=req,
                stream=True,
            )
            end = time.perf_counter()
            logger.info(f"gpt_sovits Time to make POST: {end-start}s")

            if res.status_code != 200:
                logger.error("Error:%s", res.text)
                return
                
            first = True
        
            for chunk in res.iter_content(chunk_size=None): #12800 1280 32K*20ms*2
                logger.info('chunk len:%d',len(chunk))
                if first:
                    end = time.perf_counter()
                    logger.info(f"gpt_sovits Time to first chunk: {end-start}s")
                    first = False
                if chunk and self.state==State.RUNNING:
                    yield chunk
            #print("gpt_sovits response.elapsed:", res.elapsed)
        except Exception as e:
            logger.exception('sovits')

    def __create_bytes_stream(self,byte_stream):
        #byte_stream=BytesIO(buffer)
        stream, sample_rate = sf.read(byte_stream) # [T*sample_rate,] float64
        logger.info(f'[INFO]tts audio stream {sample_rate}: {stream.shape}')
        stream = stream.astype(np.float32)

        if stream.ndim > 1:
            logger.info(f'[WARN] audio has {stream.shape[1]} channels, only use the first.')
            stream = stream[:, 0]
    
        if sample_rate != self.sample_rate and stream.shape[0]>0:
            logger.info(f'[WARN] audio sample rate is {sample_rate}, resampling into {self.sample_rate}.')
            stream = resampy.resample(x=stream, sr_orig=sample_rate, sr_new=self.sample_rate)

        return stream

    def stream_tts(self,audio_stream,msg):
        text,textevent = msg
        first = True
        # 使用更小的chunk来提高响应速度（10ms而不是20ms）
        mini_chunk = self.chunk // 2  # 160 samples = 10ms

        for chunk in audio_stream:
            # 检查中断状态
            if hasattr(self.parent, 'interrupt_audio') and self.parent.interrupt_audio:
                print(">>>>>>>>>>TTS流处理过程中检测到打断")
                return

            if chunk is not None and len(chunk)>0:
                byte_stream=BytesIO(chunk)
                stream = self.__create_bytes_stream(byte_stream)
                streamlen = stream.shape[0]
                idx=0

                # 使用更小的chunk进行处理，每次处理都检查打断标志
                while streamlen >= mini_chunk:
                    # 在每个小片段处理前都检查打断标志
                    if hasattr(self.parent, 'interrupt_audio') and self.parent.interrupt_audio:
                        print(">>>>>>>>>>TTS流处理过程中检测到打断（小片段级别）")
                        return

                    eventpoint=None
                    if first:
                        eventpoint={'status':'start','text':text,'msgevent':textevent}
                        first = False

                    # 发送更小的音频片段
                    self.parent.put_audio_frame(stream[idx:idx+mini_chunk],eventpoint)
                    streamlen -= mini_chunk
                    idx += mini_chunk

                # 处理剩余的音频数据（如果有的话）
                if streamlen > 0:
                    if hasattr(self.parent, 'interrupt_audio') and self.parent.interrupt_audio:
                        return
                    # 用零填充到mini_chunk大小
                    remaining = np.zeros(mini_chunk, dtype=np.float32)
                    remaining[:streamlen] = stream[idx:idx+streamlen]
                    self.parent.put_audio_frame(remaining, None)

        eventpoint={'status':'end','text':text,'msgevent':textevent}
        self.parent.put_audio_frame(np.zeros(self.chunk,np.float32),eventpoint)

###########################################################################################
class CosyVoiceTTS(BaseTTS):
    def txt_to_audio(self,msg):
        text,textevent = msg 
        self.stream_tts(
            self.cosy_voice(
                text,
                self.opt.REF_FILE,  
                self.opt.REF_TEXT,
                "zh", #en args.language,
                self.opt.TTS_SERVER, #"http://127.0.0.1:5000", #args.server_url,
            ),
            msg
        )

    def cosy_voice(self, text, reffile, reftext,language, server_url) -> Iterator[bytes]:
        start = time.perf_counter()
        payload = {
            'tts_text': text,
            'prompt_text': reftext
        }
        try:
            files = [('prompt_wav', ('prompt_wav', open(reffile, 'rb'), 'application/octet-stream'))]
            res = requests.request("GET", f"{server_url}/inference_zero_shot", data=payload, files=files, stream=True)
            
            end = time.perf_counter()
            logger.info(f"cosy_voice Time to make POST: {end-start}s")

            if res.status_code != 200:
                logger.error("Error:%s", res.text)
                return
                
            first = True
        
            for chunk in res.iter_content(chunk_size=9600): # 960 24K*20ms*2
                if first:
                    end = time.perf_counter()
                    logger.info(f"cosy_voice Time to first chunk: {end-start}s")
                    first = False
                if chunk and self.state==State.RUNNING:
                    yield chunk
        except Exception as e:
            logger.exception('cosyvoice')

    def stream_tts(self,audio_stream,msg):
        text,textevent = msg
        first = True
        # 使用更小的chunk来提高响应速度（10ms而不是20ms）
        mini_chunk = self.chunk // 2  # 160 samples = 10ms

        for chunk in audio_stream:
            # 检查中断状态
            if hasattr(self.parent, 'interrupt_audio') and self.parent.interrupt_audio:
                print(">>>>>>>>>>TTS流处理过程中检测到打断")
                return

            if chunk is not None and len(chunk)>0:
                stream = np.frombuffer(chunk, dtype=np.int16).astype(np.float32) / 32767
                stream = resampy.resample(x=stream, sr_orig=24000, sr_new=self.sample_rate)
                streamlen = stream.shape[0]
                idx=0

                # 使用更小的chunk进行处理，每次处理都检查打断标志
                while streamlen >= mini_chunk:
                    # 在每个小片段处理前都检查打断标志
                    if hasattr(self.parent, 'interrupt_audio') and self.parent.interrupt_audio:
                        print(">>>>>>>>>>TTS流处理过程中检测到打断（小片段级别）")
                        return

                    eventpoint=None
                    if first:
                        eventpoint={'status':'start','text':text,'msgevent':textevent}
                        first = False

                    # 发送更小的音频片段
                    self.parent.put_audio_frame(stream[idx:idx+mini_chunk],eventpoint)
                    streamlen -= mini_chunk
                    idx += mini_chunk

                # 处理剩余的音频数据（如果有的话）
                if streamlen > 0:
                    if hasattr(self.parent, 'interrupt_audio') and self.parent.interrupt_audio:
                        return
                    # 用零填充到mini_chunk大小
                    remaining = np.zeros(mini_chunk, dtype=np.float32)
                    remaining[:streamlen] = stream[idx:idx+streamlen]
                    self.parent.put_audio_frame(remaining, None)

        eventpoint={'status':'end','text':text,'msgevent':textevent}
        self.parent.put_audio_frame(np.zeros(self.chunk,np.float32),eventpoint)

###########################################################################################
_PROTOCOL = "https://"
_HOST = "tts.cloud.tencent.com"
_PATH = "/stream"
_ACTION = "TextToStreamAudio"

class TencentTTS(BaseTTS):
    def __init__(self, opt, parent):
        super().__init__(opt,parent)
        self.appid = os.getenv("TENCENT_APPID")
        self.secret_key = os.getenv("TENCENT_SECRET_KEY")
        self.secret_id = os.getenv("TENCENT_SECRET_ID")
        self.voice_type = int(opt.REF_FILE)
        self.codec = "pcm"
        self.sample_rate = 16000
        self.volume = 0
        self.speed = 0
    
    def __gen_signature(self, params):
        sort_dict = sorted(params.keys())
        sign_str = "POST" + _HOST + _PATH + "?"
        for key in sort_dict:
            sign_str = sign_str + key + "=" + str(params[key]) + '&'
        sign_str = sign_str[:-1]
        hmacstr = hmac.new(self.secret_key.encode('utf-8'),
                           sign_str.encode('utf-8'), hashlib.sha1).digest()
        s = base64.b64encode(hmacstr)
        s = s.decode('utf-8')
        return s

    def __gen_params(self, session_id, text):
        params = dict()
        params['Action'] = _ACTION
        params['AppId'] = int(self.appid)
        params['SecretId'] = self.secret_id
        params['ModelType'] = 1
        params['VoiceType'] = self.voice_type
        params['Codec'] = self.codec
        params['SampleRate'] = self.sample_rate
        params['Speed'] = self.speed
        params['Volume'] = self.volume
        params['SessionId'] = session_id
        params['Text'] = text

        timestamp = int(time.time())
        params['Timestamp'] = timestamp
        params['Expired'] = timestamp + 24 * 60 * 60
        return params

    def txt_to_audio(self,msg):
        text,textevent = msg 
        self.stream_tts(
            self.tencent_voice(
                text,
                self.opt.REF_FILE,  
                self.opt.REF_TEXT,
                "zh", #en args.language,
                self.opt.TTS_SERVER, #"http://127.0.0.1:5000", #args.server_url,
            ),
            msg
        )

    def tencent_voice(self, text, reffile, reftext,language, server_url) -> Iterator[bytes]:
        start = time.perf_counter()
        session_id = str(uuid.uuid1())
        params = self.__gen_params(session_id, text)
        signature = self.__gen_signature(params)
        headers = {
            "Content-Type": "application/json",
            "Authorization": str(signature)
        }
        url = _PROTOCOL + _HOST + _PATH
        try:
            res = requests.post(url, headers=headers,
                          data=json.dumps(params), stream=True)
            
            end = time.perf_counter()
            logger.info(f"tencent Time to make POST: {end-start}s")
                
            first = True
        
            for chunk in res.iter_content(chunk_size=6400): # 640 16K*20ms*2
                #logger.info('chunk len:%d',len(chunk))
                if first:
                    try:
                        rsp = json.loads(chunk)
                        #response["Code"] = rsp["Response"]["Error"]["Code"]
                        #response["Message"] = rsp["Response"]["Error"]["Message"]
                        logger.error("tencent tts:%s",rsp["Response"]["Error"]["Message"])
                        return
                    except:
                        end = time.perf_counter()
                        logger.info(f"tencent Time to first chunk: {end-start}s")
                        first = False                    
                if chunk and self.state==State.RUNNING:
                    yield chunk
        except Exception as e:
            logger.exception('tencent')

    def stream_tts(self,audio_stream,msg):
        text,textevent = msg
        first = True
        last_stream = np.array([],dtype=np.float32)
        # 使用更小的chunk来提高响应速度（10ms而不是20ms）
        mini_chunk = self.chunk // 2  # 160 samples = 10ms

        for chunk in audio_stream:
            # 检查中断状态
            if hasattr(self.parent, 'interrupt_audio') and self.parent.interrupt_audio:
                print(">>>>>>>>>>TTS流处理过程中检测到打断")
                return

            if chunk is not None and len(chunk)>0:
                stream = np.frombuffer(chunk, dtype=np.int16).astype(np.float32) / 32767
                stream = np.concatenate((last_stream,stream))
                streamlen = stream.shape[0]
                idx=0

                # 使用更小的chunk进行处理，每次处理都检查打断标志
                while streamlen >= mini_chunk:
                    # 在每个小片段处理前都检查打断标志
                    if hasattr(self.parent, 'interrupt_audio') and self.parent.interrupt_audio:
                        print(">>>>>>>>>>TTS流处理过程中检测到打断（小片段级别）")
                        return

                    eventpoint=None
                    if first:
                        eventpoint={'status':'start','text':text,'msgevent':textevent}
                        first = False

                    # 发送更小的音频片段
                    self.parent.put_audio_frame(stream[idx:idx+mini_chunk],eventpoint)
                    streamlen -= mini_chunk
                    idx += mini_chunk

                last_stream = stream[idx:] #get the remain stream

        eventpoint={'status':'end','text':text,'msgevent':textevent}
        self.parent.put_audio_frame(np.zeros(self.chunk,np.float32),eventpoint)

###########################################################################################

class XTTS(BaseTTS):
    def __init__(self, opt, parent):
        super().__init__(opt,parent)
        self.speaker = self.get_speaker(opt.REF_FILE, opt.TTS_SERVER)

    def txt_to_audio(self,msg):
        text,textevent = msg  
        self.stream_tts(
            self.xtts(
                text,
                self.speaker,
                "zh-cn", #en args.language,
                self.opt.TTS_SERVER, #"http://localhost:9000", #args.server_url,
                "20" #args.stream_chunk_size
            ),
            msg
        )

    def get_speaker(self,ref_audio,server_url):
        files = {"wav_file": ("reference.wav", open(ref_audio, "rb"))}
        response = requests.post(f"{server_url}/clone_speaker", files=files)
        return response.json()

    def xtts(self,text, speaker, language, server_url, stream_chunk_size) -> Iterator[bytes]:
        start = time.perf_counter()
        speaker["text"] = text
        speaker["language"] = language
        # 使用更小的chunk_size来提高响应速度
        speaker["stream_chunk_size"] = "10"  # 减少chunk大小以获得更快的响应
        try:
            res = requests.post(
                f"{server_url}/tts_stream",
                json=speaker,
                stream=True,
            )
            end = time.perf_counter()
            logger.info(f"xtts Time to make POST: {end-start}s")

            if res.status_code != 200:
                print("Error:", res.text)
                return

            first = True
        
            for chunk in res.iter_content(chunk_size=9600): #24K*20ms*2
                if first:
                    end = time.perf_counter()
                    logger.info(f"xtts Time to first chunk: {end-start}s")
                    first = False
                if chunk:
                    yield chunk
        except Exception as e:
            print(e)
    
    def stream_tts(self,audio_stream,msg):
        text,textevent = msg
        first = True
        # 使用更小的chunk来提高响应速度（10ms而不是20ms）
        mini_chunk = self.chunk // 2  # 160 samples = 10ms

        for chunk in audio_stream:
            # 检查中断状态
            if hasattr(self.parent, 'interrupt_audio') and self.parent.interrupt_audio:
                print(">>>>>>>>>>TTS流处理过程中检测到打断")
                return

            if chunk is not None and len(chunk)>0:
                stream = np.frombuffer(chunk, dtype=np.int16).astype(np.float32) / 32767
                stream = resampy.resample(x=stream, sr_orig=24000, sr_new=self.sample_rate)
                streamlen = stream.shape[0]
                idx=0

                # 使用更小的chunk进行处理，每次处理都检查打断标志
                while streamlen >= mini_chunk:
                    # 在每个小片段处理前都检查打断标志
                    if hasattr(self.parent, 'interrupt_audio') and self.parent.interrupt_audio:
                        print(">>>>>>>>>>TTS流处理过程中检测到打断（小片段级别）")
                        return

                    eventpoint=None
                    if first:
                        eventpoint={'status':'start','text':text,'msgevent':textevent}
                        first = False

                    # 发送更小的音频片段
                    self.parent.put_audio_frame(stream[idx:idx+mini_chunk],eventpoint)
                    streamlen -= mini_chunk
                    idx += mini_chunk

                # 处理剩余的音频数据（如果有的话）
                if streamlen > 0:
                    if hasattr(self.parent, 'interrupt_audio') and self.parent.interrupt_audio:
                        return
                    # 用零填充到mini_chunk大小
                    remaining = np.zeros(mini_chunk, dtype=np.float32)
                    remaining[:streamlen] = stream[idx:idx+streamlen]
                    self.parent.put_audio_frame(remaining, None)

        eventpoint={'status':'end','text':text,'msgevent':textevent}
        self.parent.put_audio_frame(np.zeros(self.chunk,np.float32),eventpoint)
