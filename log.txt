DEBUG:logger:sleep qsize=10
DEBUG:logger:sleep qsize=9
DEBUG:logger:sleep qsize=8
INFO:logger:------actual avg final fps:18.5840
DEBUG:logger:sleep qsize=9
DEBUG:logger:sleep qsize=10
[libx264 @ 0x7f364400f800] frame I:1     Avg QP:31.54  size: 14998
[libx264 @ 0x7f364400f800] frame P:126   Avg QP:23.50  size:  4011
[libx264 @ 0x7f364400f800] mb I  I16..4: 53.1%  0.0% 46.9%
[libx264 @ 0x7f364400f800] mb P  I16..4:  0.7%  0.0%  0.3%  P16..4: 34.5%  6.4%  2.3%  0.0%  0.0%    skip:55.8%
[libx264 @ 0x7f364400f800] final ratefactor: 25.06
[libx264 @ 0x7f364400f800] coded y,uvDC,uvAC intra: 32.4% 61.2% 8.9% inter: 9.1% 17.4% 0.1%
[libx264 @ 0x7f364400f800] i16 v,h,dc,p: 22% 19%  8% 51%
[libx264 @ 0x7f364400f800] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 29% 12% 22%  8%  7%  6%  5%  9%  4%
[libx264 @ 0x7f364400f800] i8c dc,h,v,p: 59% 15% 24%  3%
[libx264 @ 0x7f364400f800] ref P L0: 79.4% 11.4%  9.2%
[libx264 @ 0x7f364400f800] kb/s:983.31
[libx264 @ 0x7f36441fee80] using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2
[libx264 @ 0x7f36441fee80] profile Constrained Baseline, level 3.1, 4:2:0, 8-bit
DEBUG:logger:sleep qsize=9
DEBUG:logger:sleep qsize=9
DEBUG:logger:sleep qsize=9
[libx264 @ 0x7f36441fee80] frame I:1     Avg QP:32.79  size: 13589
[libx264 @ 0x7f36441fee80] frame P:40    Avg QP:26.16  size:  3090
[libx264 @ 0x7f36441fee80] mb I  I16..4: 54.9%  0.0% 45.1%
[libx264 @ 0x7f36441fee80] mb P  I16..4:  0.7%  0.0%  0.1%  P16..4: 30.5%  4.9%  1.7%  0.0%  0.0%    skip:62.2%
[libx264 @ 0x7f36441fee80] final ratefactor: 26.51
[libx264 @ 0x7f36441fee80] coded y,uvDC,uvAC intra: 31.0% 51.0% 6.0% inter: 7.4% 12.7% 0.1%
[libx264 @ 0x7f36441fee80] i16 v,h,dc,p: 26% 18% 11% 45%
[libx264 @ 0x7f36441fee80] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 28% 15% 26%  8%  6%  5%  5%  5%  3%
[libx264 @ 0x7f36441fee80] i8c dc,h,v,p: 61% 16% 21%  2%
[libx264 @ 0x7f36441fee80] ref P L0: 81.5%  9.9%  8.6%
[libx264 @ 0x7f36441fee80] kb/s:803.04
[libx264 @ 0x7f364402ecc0] using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2
[libx264 @ 0x7f364402ecc0] profile Constrained Baseline, level 3.1, 4:2:0, 8-bit
DEBUG:logger:sleep qsize=10
DEBUG:logger:sleep qsize=10
INFO:logger:------actual avg final fps:25.1159
[libx264 @ 0x7f364402ecc0] frame I:1     Avg QP:31.90  size: 14767
[libx264 @ 0x7f364402ecc0] frame P:38    Avg QP:26.59  size:  3627
[libx264 @ 0x7f364402ecc0] mb I  I16..4: 52.8%  0.0% 47.2%
[libx264 @ 0x7f364402ecc0] mb P  I16..4:  0.8%  0.0%  0.4%  P16..4: 33.2%  5.6%  2.0%  0.0%  0.0%    skip:57.9%
[libx264 @ 0x7f364402ecc0] final ratefactor: 26.72
[libx264 @ 0x7f364402ecc0] coded y,uvDC,uvAC intra: 37.2% 54.3% 7.2% inter: 8.7% 14.3% 0.1%
[libx264 @ 0x7f364402ecc0] i16 v,h,dc,p: 27% 17% 10% 46%
[libx264 @ 0x7f364402ecc0] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 29% 13% 23%  8%  7%  6%  5%  6%  4%
[libx264 @ 0x7f364402ecc0] i8c dc,h,v,p: 61% 15% 22%  2%
[libx264 @ 0x7f364402ecc0] ref P L0: 82.9%  9.5%  7.7%
[libx264 @ 0x7f364402ecc0] kb/s:939.11
[libx264 @ 0x7f35883c6e00] using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2
[libx264 @ 0x7f35883c6e00] profile Constrained Baseline, level 3.1, 4:2:0, 8-bit
DEBUG:logger:sleep qsize=8
DEBUG:logger:sleep qsize=9
DEBUG:logger:sleep qsize=10
DEBUG:logger:sleep qsize=11
[libx264 @ 0x7f35883c6e00] frame I:1     Avg QP:30.97  size: 16384
[libx264 @ 0x7f35883c6e00] frame P:63    Avg QP:22.29  size:  3847
[libx264 @ 0x7f35883c6e00] mb I  I16..4: 50.8%  0.0% 49.2%
[libx264 @ 0x7f35883c6e00] mb P  I16..4:  0.8%  0.0%  0.1%  P16..4: 33.6%  5.2%  2.1%  0.0%  0.0%    skip:58.3%
[libx264 @ 0x7f35883c6e00] final ratefactor: 23.83
[libx264 @ 0x7f35883c6e00] coded y,uvDC,uvAC intra: 30.4% 62.5% 11.1% inter: 8.9% 19.4% 0.4%
[libx264 @ 0x7f35883c6e00] i16 v,h,dc,p: 22% 21%  8% 48%
[libx264 @ 0x7f35883c6e00] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 31% 13% 22%  8%  6%  5%  5%  5%  4%
[libx264 @ 0x7f35883c6e00] i8c dc,h,v,p: 56% 17% 24%  3%
[libx264 @ 0x7f35883c6e00] ref P L0: 80.8% 10.3%  8.9%
[libx264 @ 0x7f35883c6e00] kb/s:970.23
[libx264 @ 0x7f35883bc080] using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2
[libx264 @ 0x7f35883bc080] profile Constrained Baseline, level 3.1, 4:2:0, 8-bit
DEBUG:logger:sleep qsize=12
INFO:logger:------actual avg final fps:25.0031
[libx264 @ 0x7f35883bc080] frame I:1     Avg QP:30.11  size: 18098
[libx264 @ 0x7f35883bc080] frame P:32    Avg QP:24.63  size:  4410
[libx264 @ 0x7f35883bc080] mb I  I16..4: 47.6%  0.0% 52.4%
[libx264 @ 0x7f35883bc080] mb P  I16..4:  0.9%  0.0%  0.4%  P16..4: 34.2%  6.7%  2.5%  0.0%  0.0%    skip:55.2%
[libx264 @ 0x7f35883bc080] final ratefactor: 25.07
[libx264 @ 0x7f35883bc080] coded y,uvDC,uvAC intra: 39.3% 61.9% 10.4% inter: 10.8% 17.3% 0.1%
[libx264 @ 0x7f35883bc080] i16 v,h,dc,p: 23% 18% 10% 48%
[libx264 @ 0x7f35883bc080] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 29% 13% 21%  8%  7%  6%  5%  7%  4%
[libx264 @ 0x7f35883bc080] i8c dc,h,v,p: 55% 17% 25%  3%
[libx264 @ 0x7f35883bc080] ref P L0: 83.1%  9.5%  7.4%
[libx264 @ 0x7f35883bc080] kb/s:1158.00
[libx264 @ 0x7f35883c9ac0] using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2
[libx264 @ 0x7f35883c9ac0] profile Constrained Baseline, level 3.1, 4:2:0, 8-bit
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
DEBUG:logger:sleep qsize=11
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
DEBUG:logger:sleep qsize=18
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
DEBUG:logger:sleep qsize=18
INFO:logger:llm time to first chunk: 1.5377663229992322s
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
DEBUG:logger:sleep qsize=19
INFO:logger:好的！这是一个关于一只小狐狸的故事：
[libx264 @ 0x7f35883c9ac0] frame I:1     Avg QP:29.16  size: 19022
[libx264 @ 0x7f35883c9ac0] frame P:55    Avg QP:22.58  size:  4847
[libx264 @ 0x7f35883c9ac0] mb I  I16..4: 45.3%  0.0% 54.7%
[libx264 @ 0x7f35883c9ac0] mb P  I16..4:  0.6%  0.0%  0.1%  P16..4: 36.4%  7.9%  3.1%  0.0%  0.0%    skip:52.0%
[libx264 @ 0x7f35883c9ac0] final ratefactor: 22.95
[libx264 @ 0x7f35883c9ac0] coded y,uvDC,uvAC intra: 36.5% 64.1% 11.0% inter: 11.5% 18.8% 0.2%
[libx264 @ 0x7f35883c9ac0] i16 v,h,dc,p: 20% 20%  8% 52%
[libx264 @ 0x7f35883c9ac0] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 29% 14% 20%  9%  7%  6%  5%  6%  4%
[libx264 @ 0x7f35883c9ac0] i8c dc,h,v,p: 56% 18% 24%  3%
[libx264 @ 0x7f35883c9ac0] ref P L0: 79.3% 11.6%  9.0%
[libx264 @ 0x7f35883c9ac0] kb/s:1224.03
[libx264 @ 0x7f35680163c0] using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2
[libx264 @ 0x7f35680163c0] profile Constrained Baseline, level 3.1, 4:2:0, 8-bit
INFO:logger:

在一个遥远的森林里，
DEBUG:logger:sleep qsize=12
INFO:logger:住着一只名叫菲菲的小狐狸。
DEBUG:logger:sleep qsize=8
[libx264 @ 0x7f35680163c0] frame I:1     Avg QP:28.28  size: 20425
[libx264 @ 0x7f35680163c0] frame P:33    Avg QP:22.92  size:  5716
[libx264 @ 0x7f35680163c0] mb I  I16..4: 43.6%  0.0% 56.4%
[libx264 @ 0x7f35680163c0] mb P  I16..4:  0.8%  0.0%  0.5%  P16..4: 37.8%  8.8%  3.3%  0.0%  0.0%    skip:48.8%
[libx264 @ 0x7f35680163c0] final ratefactor: 23.40
[libx264 @ 0x7f35680163c0] coded y,uvDC,uvAC intra: 45.1% 68.5% 11.7% inter: 13.6% 21.6% 0.2%
[libx264 @ 0x7f35680163c0] i16 v,h,dc,p: 21% 19%  7% 53%
[libx264 @ 0x7f35680163c0] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 28% 13% 20%  8%  7%  7%  5%  8%  5%
[libx264 @ 0x7f35680163c0] i8c dc,h,v,p: 50% 20% 27%  3%
[libx264 @ 0x7f35680163c0] ref P L0: 81.2% 10.5%  8.3%
[libx264 @ 0x7f35680163c0] kb/s:1475.66
[libx264 @ 0x7f3569215b80] using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2
[libx264 @ 0x7f3569215b80] profile Constrained Baseline, level 3.1, 4:2:0, 8-bit
INFO:logger:------actual avg final fps:24.8549
INFO:logger:菲菲有一双特别敏锐的眼睛，
DEBUG:logger:sleep qsize=10
INFO:logger:-------edge tts time:2.0756s
INFO:logger:[INFO]tts audio stream 24000: (108864,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
INFO:logger:能在黑夜中看清楚每一片叶子。
DEBUG:logger:sleep qsize=8
DEBUG:logger:sleep qsize=17
INFO:logger:但她有一个梦想——想学会飞翔。
DEBUG:logger:sleep qsize=20
INFO:logger:

每天，菲菲都会跑到山顶去看老鹰在天空中翱翔。
INFO:logger:notify:{'status': 'start', 'text': '好的！这是一个关于一只小狐狸的故事：', 'msgevent': None}
INFO:logger:-------edge tts time:1.2848s
INFO:logger:[INFO]tts audio stream 24000: (62784,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
DEBUG:logger:sleep qsize=21
INFO:logger:“如果他们能飞，为什么我不能？
DEBUG:logger:sleep qsize=18
[libx264 @ 0x7f3569215b80] frame I:1     Avg QP:27.10  size: 23103
[libx264 @ 0x7f3569215b80] frame P:83    Avg QP:18.58  size:  6050
[libx264 @ 0x7f3569215b80] mb I  I16..4: 42.0%  0.0% 58.0%
[libx264 @ 0x7f3569215b80] mb P  I16..4:  0.4%  0.0%  0.2%  P16..4: 39.7%  9.3%  4.0%  0.0%  0.0%    skip:46.5%
[libx264 @ 0x7f3569215b80] final ratefactor: 19.80
[libx264 @ 0x7f3569215b80] coded y,uvDC,uvAC intra: 45.4% 74.0% 21.9% inter: 13.5% 22.8% 1.4%
[libx264 @ 0x7f3569215b80] i16 v,h,dc,p: 22% 19%  7% 52%
[libx264 @ 0x7f3569215b80] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 29% 15% 20%  8%  7%  6%  5%  7%  5%
[libx264 @ 0x7f3569215b80] i8c dc,h,v,p: 46% 20% 30%  5%
[libx264 @ 0x7f3569215b80] ref P L0: 79.1% 11.9%  8.9%
[libx264 @ 0x7f3569215b80] kb/s:1500.71
[libx264 @ 0x7f3564020640] using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2
[libx264 @ 0x7f3564020640] profile Constrained Baseline, level 3.1, 4:2:0, 8-bit
INFO:logger:”她自言自语道。于是，
DEBUG:logger:sleep qsize=20
INFO:logger:------actual avg final fps:25.1468
DEBUG:logger:sleep qsize=19
[libx264 @ 0x7f3564020640] frame I:1     Avg QP:26.33  size: 25870
[libx264 @ 0x7f3564020640] frame P:40    Avg QP:20.83  size:  6957
[libx264 @ 0x7f3564020640] mb I  I16..4: 38.2%  0.0% 61.8%
[libx264 @ 0x7f3564020640] mb P  I16..4:  1.4%  0.0%  0.4%  P16..4: 42.7% 11.5%  4.4%  0.0%  0.0%    skip:39.5%
[libx264 @ 0x7f3564020640] final ratefactor: 21.39
[libx264 @ 0x7f3564020640] coded y,uvDC,uvAC intra: 42.6% 76.6% 16.5% inter: 16.4% 27.6% 0.8%
[libx264 @ 0x7f3564020640] i16 v,h,dc,p: 21% 19%  6% 54%
[libx264 @ 0x7f3564020640] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 29% 14% 20%  8%  7%  6%  5%  6%  5%
[libx264 @ 0x7f3564020640] i8c dc,h,v,p: 50% 17% 28%  5%
[libx264 @ 0x7f3564020640] ref P L0: 82.3% 10.1%  7.7%
[libx264 @ 0x7f3564020640] kb/s:1780.37
[libx264 @ 0x7f35887f8740] using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2
[libx264 @ 0x7f35887f8740] profile Constrained Baseline, level 3.1, 4:2:0, 8-bit
DEBUG:logger:sleep qsize=21
INFO:logger:------actual avg infer fps:90.7715
DEBUG:logger:sleep qsize=19
INFO:logger:她开始尝试各种方法：用树叶做成“翅膀”，
DEBUG:logger:sleep qsize=19
INFO:logger:notify:{'status': 'end', 'text': '好的！这是一个关于一只小狐狸的故事：', 'msgevent': None}
INFO:logger:notify:{'status': 'start', 'text': '\n\n在一个遥远的森林里，', 'msgevent': None}
DEBUG:logger:sleep qsize=20
INFO:logger:从树上跳下去；用松鼠的建议绑上羽毛……但每次都以摔倒告终。
[libx264 @ 0x7f35887f8740] frame I:1     Avg QP:25.33  size: 27784
[libx264 @ 0x7f35887f8740] frame P:63    Avg QP:16.77  size:  7967
[libx264 @ 0x7f35887f8740] mb I  I16..4: 36.5%  0.0% 63.5%
[libx264 @ 0x7f35887f8740] mb P  I16..4:  1.0%  0.0%  0.6%  P16..4: 44.4% 12.2%  5.4%  0.0%  0.0%    skip:36.4%
[libx264 @ 0x7f35887f8740] final ratefactor: 18.64
[libx264 @ 0x7f35887f8740] coded y,uvDC,uvAC intra: 51.0% 83.2% 37.3% inter: 20.1% 27.3% 3.6%
[libx264 @ 0x7f35887f8740] i16 v,h,dc,p: 16% 14%  7% 63%
[libx264 @ 0x7f35887f8740] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 27% 19% 20%  7%  7%  5%  5%  5%  4%
[libx264 @ 0x7f35887f8740] i8c dc,h,v,p: 49% 19% 27%  5%
[libx264 @ 0x7f35887f8740] ref P L0: 81.3% 10.9%  7.7%
[libx264 @ 0x7f35887f8740] kb/s:1986.50
[libx264 @ 0x7f356800a900] using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2
[libx264 @ 0x7f356800a900] profile Constrained Baseline, level 3.1, 4:2:0, 8-bit
DEBUG:logger:sleep qsize=21
INFO:logger:------actual avg final fps:25.0018
DEBUG:logger:sleep qsize=11
[libx264 @ 0x7f356800a900] frame I:1     Avg QP:24.46  size: 29882
[libx264 @ 0x7f356800a900] frame P:38    Avg QP:19.75  size:  8748
[libx264 @ 0x7f356800a900] mb I  I16..4: 33.9%  0.0% 66.1%
[libx264 @ 0x7f356800a900] mb P  I16..4:  1.5%  0.0%  0.7%  P16..4: 43.4% 12.9%  4.9%  0.0%  0.0%    skip:36.7%
[libx264 @ 0x7f356800a900] final ratefactor: 20.28
[libx264 @ 0x7f356800a900] coded y,uvDC,uvAC intra: 47.4% 80.7% 22.7% inter: 20.8% 30.7% 1.3%
[libx264 @ 0x7f356800a900] i16 v,h,dc,p: 20% 18%  5% 57%
[libx264 @ 0x7f356800a900] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 29% 15% 18%  8%  7%  6%  5%  7%  5%
[libx264 @ 0x7f356800a900] i8c dc,h,v,p: 48% 18% 29%  5%
[libx264 @ 0x7f356800a900] ref P L0: 83.2% 10.1%  6.7%
[libx264 @ 0x7f356800a900] kb/s:2229.69
[libx264 @ 0x7f35887ff7c0] using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2
[libx264 @ 0x7f35887ff7c0] profile Constrained Baseline, level 3.1, 4:2:0, 8-bit
INFO:logger:

一天，一只受伤的老鹰落在了菲菲面前。
DEBUG:logger:sleep qsize=10
INFO:logger:notify:{'status': 'end', 'text': '\n\n在一个遥远的森林里，', 'msgevent': None}
INFO:logger:菲菲小心翼翼地照顾它，
DEBUG:logger:sleep qsize=9
INFO:logger:给它找食物、包扎伤口。
INFO:logger:渐渐地，老鹰恢复了健康，
DEBUG:logger:sleep qsize=9
DEBUG:logger:sleep qsize=10
[libx264 @ 0x7f35887ff7c0] frame I:1     Avg QP:23.68  size: 33815
[libx264 @ 0x7f35887ff7c0] frame P:66    Avg QP:15.58  size:  9364
[libx264 @ 0x7f35887ff7c0] mb I  I16..4: 32.5%  0.0% 67.5%
[libx264 @ 0x7f35887ff7c0] mb P  I16..4:  0.5%  0.0%  0.4%  P16..4: 43.3% 11.3%  6.3%  0.0%  0.0%    skip:38.2%
[libx264 @ 0x7f35887ff7c0] final ratefactor: 17.11
[libx264 @ 0x7f35887ff7c0] coded y,uvDC,uvAC intra: 65.1% 87.8% 42.4% inter: 21.4% 26.6% 5.9%
[libx264 @ 0x7f35887ff7c0] i16 v,h,dc,p: 17% 14%  5% 64%
[libx264 @ 0x7f35887ff7c0] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 27% 17% 19%  7%  8%  6%  6%  6%  5%
[libx264 @ 0x7f35887ff7c0] i8c dc,h,v,p: 46% 19% 28%  6%
[libx264 @ 0x7f35887ff7c0] ref P L0: 82.5% 10.3%  7.1%
[libx264 @ 0x7f35887ff7c0] kb/s:2334.96
[libx264 @ 0x7f3568003140] using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2
[libx264 @ 0x7f3568003140] profile Constrained Baseline, level 3.1, 4:2:0, 8-bit
DEBUG:logger:sleep qsize=11
INFO:logger:------actual avg final fps:24.9950
DEBUG:logger:sleep qsize=9
INFO:logger:并对菲菲说：“你为什么总是想飞呢？
DEBUG:logger:sleep qsize=10
INFO:logger:”

菲菲低下了头，“因为我想看看更高的世界。
DEBUG:logger:sleep qsize=7
INFO:logger:-------edge tts time:11.6518s
INFO:logger:[INFO]tts audio stream 24000: (77184,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
DEBUG:logger:sleep qsize=10
INFO:logger:”

老鹰笑了笑，“其实，
DEBUG:logger:sleep qsize=18
INFO:logger:飞翔不一定是用翅膀。你跑遍森林寻找答案，
DEBUG:logger:sleep qsize=19
INFO:logger:帮助别人，这也是另一种飞翔。
INFO:logger:------actual avg infer fps:87.0835
INFO:logger:notify:{'status': 'start', 'text': '住着一只名叫菲菲的小狐狸。', 'msgevent': None}
INFO:logger:”

从那以后，菲菲明白了，
DEBUG:logger:sleep qsize=20
INFO:logger:-------edge tts time:1.5972s
INFO:logger:[INFO]tts audio stream 24000: (79488,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
INFO:logger:------actual avg final fps:24.6166
INFO:logger:虽然她没有翅膀，但她可以用自己的方式去触碰天空。
DEBUG:logger:sleep qsize=19
INFO:logger:

你喜欢这个故事吗？
INFO:logger:llm time to last chunk: 20.51758000099926s
DEBUG:logger:sleep qsize=21
DEBUG:logger:sleep qsize=18
DEBUG:logger:sleep qsize=21
INFO:logger:notify:{'status': 'end', 'text': '住着一只名叫菲菲的小狐狸。', 'msgevent': None}
INFO:logger:notify:{'status': 'start', 'text': '菲菲有一双特别敏锐的眼睛，', 'msgevent': None}
DEBUG:logger:sleep qsize=19
INFO:logger:-------edge tts time:3.1911s
INFO:logger:[INFO]tts audio stream 24000: (89856,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
DEBUG:logger:sleep qsize=18
INFO:logger:------actual avg final fps:25.2988
INFO:logger:------actual avg infer fps:90.8782
DEBUG:logger:sleep qsize=18
INFO:logger:-------edge tts time:1.2393s
INFO:logger:[INFO]tts audio stream 24000: (89280,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
DEBUG:logger:sleep qsize=19
DEBUG:logger:sleep qsize=20
INFO:logger:notify:{'status': 'end', 'text': '菲菲有一双特别敏锐的眼睛，', 'msgevent': None}
INFO:logger:notify:{'status': 'start', 'text': '能在黑夜中看清楚每一片叶子。', 'msgevent': None}
DEBUG:logger:sleep qsize=20
INFO:logger:-------edge tts time:1.4922s
INFO:logger:[INFO]tts audio stream 24000: (131904,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
DEBUG:logger:sleep qsize=19
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
DEBUG:logger:sleep qsize=20
INFO:logger:-------edge tts time:1.3530s
ERROR:logger:edgetts err!!!!!
INFO:logger:llm time to first chunk: 0.6763429039992843s
INFO:logger:------actual avg final fps:24.7806
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
DEBUG:logger:sleep qsize=20
INFO:logger:It seems like your message is a bit unclear.
DEBUG:logger:sleep qsize=11
DEBUG:logger:sleep qsize=10
INFO:logger:-------edge tts time:1.4895s
INFO:logger:[INFO]tts audio stream 24000: (77184,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
INFO:logger: Could you clarify or provide more details so I can better understand and assist you? If "tingi" refers to a specific topic,
DEBUG:logger:sleep qsize=18
INFO:logger: question, or context,
INFO:logger:------actual avg infer fps:84.2676
INFO:logger: let me know!
INFO:logger:llm time to last chunk: 3.078473789999407s
DEBUG:logger:sleep qsize=21
INFO:logger:notify:{'status': 'start', 'text': 'It seems like your message is a bit unclear.', 'msgevent': None}
DEBUG:logger:sleep qsize=19
INFO:logger:-------edge tts time:1.5423s
INFO:logger:[INFO]tts audio stream 24000: (192960,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
INFO:logger:------actual avg final fps:25.3321
DEBUG:logger:sleep qsize=19
DEBUG:logger:sleep qsize=20
INFO:logger:-------edge tts time:1.3724s
INFO:logger:[INFO]tts audio stream 24000: (65088,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
DEBUG:logger:sleep qsize=21
DEBUG:logger:sleep qsize=19
INFO:logger:notify:{'status': 'end', 'text': 'It seems like your message is a bit unclear.', 'msgevent': None}
INFO:logger:notify:{'status': 'start', 'text': ' Could you clarify or provide more details so I can better understand and assist you? If "tingi" refers to a specific topic,', 'msgevent': None}
INFO:logger:-------edge tts time:1.3213s
INFO:logger:[INFO]tts audio stream 24000: (44928,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
DEBUG:logger:sleep qsize=18
INFO:logger:------actual avg infer fps:98.9307
DEBUG:logger:sleep qsize=18
DEBUG:logger:sleep qsize=20
INFO:logger:------actual avg final fps:24.8447
DEBUG:logger:sleep qsize=20
DEBUG:logger:sleep qsize=20
DEBUG:logger:sleep qsize=19
DEBUG:logger:sleep qsize=20
DEBUG:logger:sleep qsize=19
INFO:logger:------actual avg infer fps:84.1414
DEBUG:logger:sleep qsize=21
INFO:logger:------actual avg final fps:25.1619
DEBUG:logger:sleep qsize=21
DEBUG:logger:sleep qsize=19
DEBUG:logger:sleep qsize=18
DEBUG:logger:sleep qsize=18
INFO:logger:notify:{'status': 'end', 'text': ' Could you clarify or provide more details so I can better understand and assist you? If "tingi" refers to a specific topic,', 'msgevent': None}
INFO:logger:notify:{'status': 'start', 'text': ' question, or context,', 'msgevent': None}
DEBUG:logger:sleep qsize=19
DEBUG:logger:sleep qsize=20
INFO:logger:------actual avg infer fps:81.1563
INFO:logger:------actual avg final fps:25.0020
DEBUG:logger:sleep qsize=21
DEBUG:logger:sleep qsize=17
INFO:logger:notify:{'status': 'end', 'text': ' question, or context,', 'msgevent': None}
INFO:logger:notify:{'status': 'start', 'text': ' let me know!', 'msgevent': None}
DEBUG:logger:sleep qsize=17
DEBUG:logger:sleep qsize=13
INFO:logger:notify:{'status': 'end', 'text': ' let me know!', 'msgevent': None}
DEBUG:logger:sleep qsize=8
DEBUG:logger:sleep qsize=10
INFO:logger:------actual avg final fps:24.9985
DEBUG:logger:sleep qsize=9
DEBUG:logger:sleep qsize=10
DEBUG:logger:sleep qsize=9
DEBUG:logger:sleep qsize=9
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
DEBUG:logger:sleep qsize=16
INFO:logger:llm time to first chunk: 0.5157784780003567s
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
DEBUG:logger:sleep qsize=20
INFO:logger:从前有一只小兔子，它非常喜欢吃胡萝卜。
INFO:logger:------actual avg final fps:25.0035
DEBUG:logger:sleep qsize=10
INFO:logger:一天，小兔子在森林里迷了路，
INFO:logger:遇到了一只热心的小乌龟。
DEBUG:logger:sleep qsize=9
INFO:logger:小乌龟不仅带小兔子找到了回家的路，
INFO:logger:-------edge tts time:1.4035s
INFO:logger:[INFO]tts audio stream 24000: (110592,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
DEBUG:logger:sleep qsize=15
INFO:logger:还和它成为了好朋友。从那以后，
DEBUG:logger:sleep qsize=21
INFO:logger:notify:{'status': 'start', 'text': '从前有一只小兔子，它非常喜欢吃胡萝卜。', 'msgevent': None}
INFO:logger:小兔子和小乌龟经常一起冒险，
DEBUG:logger:sleep qsize=19
INFO:logger:分享美食，度过了许多快乐的日子。
INFO:logger:-------edge tts time:1.5831s
INFO:logger:[INFO]tts audio stream 24000: (85824,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
DEBUG:logger:sleep qsize=18
INFO:logger:这个故事告诉我们，帮助别人能收获友谊和快乐。
INFO:logger:llm time to last chunk: 4.681624732000273s
INFO:logger:------actual avg infer fps:92.3436
DEBUG:logger:sleep qsize=18
INFO:logger:------actual avg final fps:24.9979
INFO:logger:-------edge tts time:1.3433s
INFO:logger:[INFO]tts audio stream 24000: (69120,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
DEBUG:logger:sleep qsize=19
DEBUG:logger:sleep qsize=19
DEBUG:logger:sleep qsize=20
INFO:logger:-------edge tts time:1.3846s
INFO:logger:[INFO]tts audio stream 24000: (100224,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
DEBUG:logger:sleep qsize=19
DEBUG:logger:sleep qsize=20
INFO:logger:notify:{'status': 'end', 'text': '从前有一只小兔子，它非常喜欢吃胡萝卜。', 'msgevent': None}
INFO:logger:notify:{'status': 'start', 'text': '一天，小兔子在森林里迷了路，', 'msgevent': None}
INFO:logger:-------edge tts time:1.2605s
INFO:logger:[INFO]tts audio stream 24000: (97920,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
DEBUG:logger:sleep qsize=21
INFO:logger:------actual avg infer fps:98.3364
INFO:logger:------actual avg final fps:25.0030
DEBUG:logger:sleep qsize=18
INFO:logger:-------edge tts time:1.4633s
INFO:logger:[INFO]tts audio stream 24000: (94464,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
DEBUG:logger:sleep qsize=20
DEBUG:logger:sleep qsize=20
DEBUG:logger:sleep qsize=18
INFO:logger:-------edge tts time:1.3414s
INFO:logger:[INFO]tts audio stream 24000: (97344,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
INFO:logger:notify:{'status': 'end', 'text': '一天，小兔子在森林里迷了路，', 'msgevent': None}
INFO:logger:notify:{'status': 'start', 'text': '遇到了一只热心的小乌龟。', 'msgevent': None}
DEBUG:logger:sleep qsize=20
DEBUG:logger:sleep qsize=19
INFO:logger:-------edge tts time:1.4836s
INFO:logger:[INFO]tts audio stream 24000: (127296,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
DEBUG:logger:sleep qsize=20
INFO:logger:------actual avg final fps:25.0002
INFO:logger:------actual avg infer fps:88.8298
DEBUG:logger:sleep qsize=20
DEBUG:logger:sleep qsize=19
INFO:logger:notify:{'status': 'end', 'text': '遇到了一只热心的小乌龟。', 'msgevent': None}
INFO:logger:notify:{'status': 'start', 'text': '小乌龟不仅带小兔子找到了回家的路，', 'msgevent': None}
DEBUG:logger:sleep qsize=19
DEBUG:logger:sleep qsize=21
DEBUG:logger:sleep qsize=18
DEBUG:logger:sleep qsize=20
INFO:logger:------actual avg final fps:24.9993
DEBUG:logger:sleep qsize=20
INFO:logger:------actual avg infer fps:87.8617
DEBUG:logger:sleep qsize=19
INFO:logger:notify:{'status': 'end', 'text': '小乌龟不仅带小兔子找到了回家的路，', 'msgevent': None}
INFO:logger:notify:{'status': 'start', 'text': '还和它成为了好朋友。从那以后，', 'msgevent': None}
DEBUG:logger:sleep qsize=20
DEBUG:logger:sleep qsize=19
DEBUG:logger:sleep qsize=20
DEBUG:logger:sleep qsize=20
INFO:logger:------actual avg final fps:24.8550
DEBUG:logger:sleep qsize=19
DEBUG:logger:sleep qsize=20
INFO:logger:------actual avg infer fps:88.8840
INFO:logger:notify:{'status': 'end', 'text': '还和它成为了好朋友。从那以后，', 'msgevent': None}
INFO:logger:notify:{'status': 'start', 'text': '小兔子和小乌龟经常一起冒险，', 'msgevent': None}
DEBUG:logger:sleep qsize=17
DEBUG:logger:sleep qsize=20
DEBUG:logger:sleep qsize=18
DEBUG:logger:sleep qsize=20
INFO:logger:------actual avg final fps:25.1489
DEBUG:logger:sleep qsize=21
DEBUG:logger:sleep qsize=18
DEBUG:logger:sleep qsize=19
INFO:logger:notify:{'status': 'end', 'text': '小兔子和小乌龟经常一起冒险，', 'msgevent': None}
INFO:logger:notify:{'status': 'start', 'text': '分享美食，度过了许多快乐的日子。', 'msgevent': None}
INFO:logger:------actual avg infer fps:88.7056
DEBUG:logger:sleep qsize=18
DEBUG:logger:sleep qsize=19
DEBUG:logger:sleep qsize=20
DEBUG:logger:sleep qsize=21
INFO:logger:------actual avg final fps:24.8759
DEBUG:logger:sleep qsize=19
DEBUG:logger:sleep qsize=19
INFO:logger:notify:{'status': 'end', 'text': '分享美食，度过了许多快乐的日子。', 'msgevent': None}
INFO:logger:notify:{'status': 'start', 'text': '这个故事告诉我们，帮助别人能收获友谊和快乐。', 'msgevent': None}
DEBUG:logger:sleep qsize=20
INFO:logger:------actual avg infer fps:97.8430
DEBUG:logger:sleep qsize=19
DEBUG:logger:sleep qsize=20
DEBUG:logger:sleep qsize=20
INFO:logger:------actual avg final fps:25.1285
DEBUG:logger:sleep qsize=20
DEBUG:logger:sleep qsize=20
DEBUG:logger:sleep qsize=13
DEBUG:logger:sleep qsize=10
INFO:logger:notify:{'status': 'end', 'text': '这个故事告诉我们，帮助别人能收获友谊和快乐。', 'msgevent': None}
DEBUG:logger:sleep qsize=10
INFO:logger:------actual avg final fps:25.0006
DEBUG:logger:sleep qsize=9
DEBUG:logger:sleep qsize=9
DEBUG:logger:sleep qsize=11
DEBUG:logger:sleep qsize=8
DEBUG:logger:sleep qsize=10
DEBUG:logger:sleep qsize=9
DEBUG:logger:sleep qsize=10
INFO:logger:------actual avg final fps:24.9874
DEBUG:logger:sleep qsize=9
DEBUG:logger:sleep qsize=10
DEBUG:logger:sleep qsize=9
DEBUG:logger:sleep qsize=9
DEBUG:logger:sleep qsize=10
DEBUG:logger:sleep qsize=10
INFO:logger:------actual avg final fps:25.0171
DEBUG:logger:sleep qsize=9
DEBUG:logger:sleep qsize=8
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
DEBUG:logger:sleep qsize=13
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
INFO:logger:llm time to first chunk: 0.45547402600004716s
DEBUG:logger:sleep qsize=19
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
DEBUG:logger:sleep qsize=18
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
DEBUG:logger:sleep qsize=18
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
DEBUG:logger:sleep qsize=20
INFO:logger:------actual avg final fps:24.9364
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
>>>>>>>>>>>>>>>>>进入打断状态！
DEBUG:logger:sleep qsize=18
INFO:logger:从前有个小村庄，村里住着一位聪明的少年阿明。
DEBUG:logger:sleep qsize=13
INFO:logger:他喜欢探索大自然，常常在森林里发现新奇的事物。
INFO:logger:一天，他在树林中遇到一只受伤的小鹿，
DEBUG:logger:sleep qsize=8
INFO:logger:-------edge tts time:1.5243s
INFO:logger:[INFO]tts audio stream 24000: (120960,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
INFO:logger:便细心照料它，直到它恢复健康。
DEBUG:logger:sleep qsize=15
INFO:logger:------actual avg infer fps:93.8461
DEBUG:logger:sleep qsize=19
INFO:logger:小鹿痊愈后，每天都会带着阿明找到新鲜的水果和清澈的泉水。
INFO:logger:村民们因此不再为食物发愁，
INFO:logger:notify:{'status': 'start', 'text': '从前有个小村庄，村里住着一位聪明的少年阿明。', 'msgevent': None}
DEBUG:logger:sleep qsize=20
INFO:logger:------actual avg final fps:25.0661
DEBUG:logger:sleep qsize=20
INFO:logger:-------edge tts time:1.9470s
INFO:logger:[INFO]tts audio stream 24000: (137088,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
INFO:logger:过上了富足的生活。大家明白了，
INFO:logger:善良与分享能让生活更美好。
DEBUG:logger:sleep qsize=18
INFO:logger:从此，阿明和小鹿成了最好的朋友，
DEBUG:logger:sleep qsize=21
INFO:logger:村庄也因他们的故事被命名为“鹿鸣村”。
INFO:logger:llm time to last chunk: 8.217851667000104s
INFO:logger:-------edge tts time:1.4435s
INFO:logger:[INFO]tts audio stream 24000: (107712,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
DEBUG:logger:sleep qsize=19
DEBUG:logger:sleep qsize=19
INFO:logger:------actual avg infer fps:90.9807
DEBUG:logger:sleep qsize=20
INFO:logger:-------edge tts time:1.3652s
INFO:logger:[INFO]tts audio stream 24000: (93312,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
INFO:logger:------actual avg final fps:24.9250
DEBUG:logger:sleep qsize=20
INFO:logger:notify:{'status': 'end', 'text': '从前有个小村庄，村里住着一位聪明的少年阿明。', 'msgevent': None}
INFO:logger:notify:{'status': 'start', 'text': '他喜欢探索大自然，常常在森林里发现新奇的事物。', 'msgevent': None}
DEBUG:logger:sleep qsize=20
INFO:logger:-------edge tts time:1.6609s
INFO:logger:[INFO]tts audio stream 24000: (160128,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
DEBUG:logger:sleep qsize=19
DEBUG:logger:sleep qsize=19
DEBUG:logger:sleep qsize=18
INFO:logger:-------edge tts time:1.2877s
INFO:logger:[INFO]tts audio stream 24000: (81792,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
DEBUG:logger:sleep qsize=20
INFO:logger:------actual avg infer fps:90.8938
INFO:logger:------actual avg final fps:24.9352
DEBUG:logger:sleep qsize=20
INFO:logger:-------edge tts time:1.4777s
INFO:logger:[INFO]tts audio stream 24000: (97920,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
DEBUG:logger:sleep qsize=19
DEBUG:logger:sleep qsize=21
INFO:logger:-------edge tts time:1.3439s
DEBUG:logger:sleep qsize=20
INFO:logger:[INFO]tts audio stream 24000: (89856,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
INFO:logger:notify:{'status': 'end', 'text': '他喜欢探索大自然，常常在森林里发现新奇的事物。', 'msgevent': None}
INFO:logger:notify:{'status': 'start', 'text': '一天，他在树林中遇到一只受伤的小鹿，', 'msgevent': None}
DEBUG:logger:sleep qsize=18
DEBUG:logger:sleep qsize=22
INFO:logger:-------edge tts time:1.2901s
INFO:logger:[INFO]tts audio stream 24000: (95616,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
DEBUG:logger:sleep qsize=18
INFO:logger:------actual avg final fps:25.1417
INFO:logger:------actual avg infer fps:92.9320
DEBUG:logger:sleep qsize=21
INFO:logger:-------edge tts time:1.4588s
INFO:logger:[INFO]tts audio stream 24000: (103104,)
INFO:logger:[WARN] audio sample rate is 24000, resampling into 16000.
DEBUG:logger:sleep qsize=18
DEBUG:logger:sleep qsize=19
DEBUG:logger:sleep qsize=19
INFO:logger:notify:{'status': 'end', 'text': '一天，他在树林中遇到一只受伤的小鹿，', 'msgevent': None}
INFO:logger:notify:{'status': 'start', 'text': '便细心照料它，直到它恢复健康。', 'msgevent': None}
DEBUG:logger:sleep qsize=20
DEBUG:logger:sleep qsize=19
INFO:logger:------actual avg final fps:24.9988
DEBUG:logger:sleep qsize=20
INFO:logger:------actual avg infer fps:90.3591
DEBUG:logger:sleep qsize=19
DEBUG:logger:sleep qsize=19
DEBUG:logger:sleep qsize=20
INFO:logger:notify:{'status': 'end', 'text': '便细心照料它，直到它恢复健康。', 'msgevent': None}
INFO:logger:notify:{'status': 'start', 'text': '小鹿痊愈后，每天都会带着阿明找到新鲜的水果和清澈的泉水。', 'msgevent': None}
DEBUG:logger:sleep qsize=20
DEBUG:logger:sleep qsize=18





###  第一次打断成功,之后两次未成功,其中第次打断成功之后ai语音播报"It seems like your message is a bit unclear."