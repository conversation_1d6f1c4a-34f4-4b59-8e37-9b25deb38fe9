<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>WebRTC webcam</title>
    <style>
    button {
        padding: 8px 16px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: bold;
    }

    video {
        width: 100%;
    }

    .option {
        margin-bottom: 8px;
    }

    #media {
        max-width: 1280px;
    }

    /* 打断按钮样式 */
    .btn-danger {
        background: linear-gradient(135deg, #ff4444, #cc0000);
        color: white;
        margin-left: 10px;
        padding: 10px 20px;
        font-size: 16px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .btn-danger:hover {
        background: linear-gradient(135deg, #ff6666, #dd1111);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(255, 68, 68, 0.3);
    }

    .btn-danger:active {
        transform: translateY(0);
    }

    .btn-danger:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    /* 状态显示 */
    .status-display {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 10px 15px;
        background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
        border: 2px solid #ccc;
        border-radius: 8px;
        font-weight: bold;
        z-index: 1000;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    /* 快捷键提示 */
    .shortcut-hint {
        position: fixed;
        top: 80px;
        right: 20px;
        padding: 8px 12px;
        background: rgba(0,0,0,0.8);
        color: white;
        border-radius: 5px;
        font-family: monospace;
        font-size: 12px;
        z-index: 999;
        opacity: 0.8;
        transition: opacity 0.3s ease;
    }

    /* 按钮组样式 */
    .button-group {
        margin: 10px 0;
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        align-items: center;
    }

    .btn-primary {
        background: linear-gradient(135deg, #4299e1, #3182ce);
        color: white;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #63b3ed, #4299e1);
        transform: translateY(-1px);
    }
    </style>
</head>
<body>

<div class="option">
    <input id="use-stun" type="checkbox"/>
    <label for="use-stun">Use STUN server</label>
</div>

<!-- WebRTC控制按钮 -->
<div class="button-group">
    <button id="start" onclick="start()">Start WebRTC</button>
    <button id="stop" style="display: none" onclick="stop()">Stop WebRTC</button>
</div>

<!-- 录制控制按钮 -->
<div class="button-group">
    <button class="btn btn-primary" id="btn_start_record">📹 Start Recording</button>
    <button class="btn btn-primary" id="btn_stop_record" disabled>⏹️ Stop Recording</button>
</div>

<!-- 打断控制按钮 -->
<div class="button-group">
    <button class="btn btn-danger" id="btn_interrupt" onclick="interruptAI()">🛑 立即打断</button>
    <span style="margin-left: 10px; font-size: 12px; color: #666;">
        快捷键: <kbd>ESC</kbd> / <kbd>Ctrl+C</kbd> / <kbd>空格</kbd>
    </span>
</div>

<input type="hidden" id="sessionid" value="0">
<form class="form-inline" id="echo-form">
    <div class="form-group">
      <p>input text</p>

      <textarea cols="2" rows="3" style="width:600px;height:50px;" class="form-control" id="message">test</textarea>
    </div>
    <button type="submit" class="btn btn-default">Send</button>
  </form>

<div id="media">
    <h2>Media</h2>

    <audio id="audio" autoplay="true"></audio>
    <video id="video" style="width:600px;" autoplay="true" playsinline="true"></video>
</div>

<!-- 状态显示 -->
<div id="status" class="status-display">🟢 就绪</div>

<!-- 快捷键提示 -->
<div id="shortcutHint" class="shortcut-hint">
    <div style="font-size: 11px; margin-bottom: 3px;">快捷键打断：</div>
    <div style="font-size: 10px;">ESC / Ctrl+C / 空格键</div>
</div>

<iframe src="asr/index.html" width="600" height="500" title="ASR Interface"></iframe>

<script src="client.js"></script>
<script type="text/javascript" src="http://cdn.sockjs.org/sockjs-0.3.4.js"></script>
<script type="text/javascript" src="https://code.jquery.com/jquery-2.1.1.min.js"></script>
</body>
<script type="text/javascript" charset="utf-8">
    // 更新状态显示
    function updateStatus(text, color, bgColor, borderColor) {
        const statusElement = document.getElementById('status');
        if (statusElement) {
            statusElement.textContent = text;
            statusElement.style.color = color || 'black';
            if (bgColor) {
                statusElement.style.background = bgColor;
            }
            if (borderColor) {
                statusElement.style.borderColor = borderColor;
            }
        }
    }

    // 增强的打断AI函数
    async function interruptAI() {
        console.log('🛑 用户触发打断操作');

        const statusElement = document.getElementById('status');
        const interruptButton = document.getElementById('btn_interrupt');

        // 立即显示打断状态
        updateStatus('🔴 正在打断...', 'red', 'linear-gradient(135deg, #ffe0e0, #ffcccc)', '#ff6666');

        // 禁用打断按钮，防止重复点击
        if (interruptButton) {
            interruptButton.disabled = true;
            interruptButton.style.opacity = '0.6';
            interruptButton.innerHTML = '⏳ 打断中...';
        }

        try {
            // 使用Promise.race来设置超时，确保快速响应
            const timeoutPromise = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('Interrupt timeout')), 3000)
            );

            // 优先使用专门的打断端点
            const interruptPromise = fetch('/interrupt', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    sessionid: parseInt(document.getElementById('sessionid').value)
                })
            });

            const response = await Promise.race([interruptPromise, timeoutPromise]);

            if (response.ok) {
                const data = await response.json();
                console.log('✅ 打断请求成功:', data);

                updateStatus('✅ 打断成功', 'green', 'linear-gradient(135deg, #e0ffe0, #ccffcc)', '#66cc66');

                // 2秒后恢复就绪状态
                setTimeout(() => {
                    updateStatus('🟢 就绪', 'black', 'linear-gradient(135deg, #f0f0f0, #e0e0e0)', '#ccc');
                }, 2000);

            } else {
                console.error('❌ 打断请求失败:', response.status);
                updateStatus('❌ 打断失败', 'red', 'linear-gradient(135deg, #ffe0e0, #ffcccc)', '#ff6666');
            }

        } catch (error) {
            console.error('⚠️ 打断请求出错:', error);

            // 如果专门的打断端点失败，尝试使用human端点作为备用
            try {
                console.log('🔄 尝试备用打断方法...');
                const backupResponse = await fetch('/human', {
                    body: JSON.stringify({
                        interrupt: true,
                        sessionid: parseInt(document.getElementById('sessionid').value),
                    }),
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    method: 'POST'
                });

                if (backupResponse.ok) {
                    console.log('✅ 备用打断方法成功');
                    updateStatus('✅ 打断成功', 'green', 'linear-gradient(135deg, #e0ffe0, #ccffcc)', '#66cc66');
                    setTimeout(() => {
                        updateStatus('🟢 就绪', 'black', 'linear-gradient(135deg, #f0f0f0, #e0e0e0)', '#ccc');
                    }, 2000);
                } else {
                    updateStatus('❌ 打断失败', 'red', 'linear-gradient(135deg, #ffe0e0, #ffcccc)', '#ff6666');
                }
            } catch (backupError) {
                console.error('⚠️ 备用打断方法也失败:', backupError);
                updateStatus('⚠️ 打断错误', 'red', 'linear-gradient(135deg, #ffe0e0, #ffcccc)', '#ff6666');
            }
        }

        // 重新启用打断按钮
        if (interruptButton) {
            interruptButton.disabled = false;
            interruptButton.style.opacity = '1';
            interruptButton.innerHTML = '🛑 立即打断';
        }
    }

    // 兼容旧函数名
    function interruptSpeaking() {
        interruptAI();
    }

    // 添加键盘快捷键支持
    document.addEventListener('keydown', function(event) {
        // ESC键或Ctrl+C快速打断
        if (event.key === 'Escape' || (event.ctrlKey && event.key === 'c')) {
            event.preventDefault();
            console.log('⌨️ 检测到快捷键打断: ' + (event.key === 'Escape' ? 'ESC' : 'Ctrl+C'));
            interruptAI();
        }

        // 空格键也可以打断（如果没有在输入框中）
        if (event.key === ' ' && event.target.tagName !== 'INPUT' && event.target.tagName !== 'TEXTAREA') {
            event.preventDefault();
            console.log('⌨️ 检测到空格键打断');
            interruptAI();
        }
    });

    // 页面加载完成后的初始化
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚀 WebRTC ASR页面已加载，打断功能已启用');

        // 5秒后淡化快捷键提示
        setTimeout(function() {
            const hintElement = document.getElementById('shortcutHint');
            if (hintElement) {
                hintElement.style.opacity = '0.3';
            }
        }, 5000);

        // 10秒后完全隐藏快捷键提示
        setTimeout(function() {
            const hintElement = document.getElementById('shortcutHint');
            if (hintElement) {
                hintElement.style.display = 'none';
            }
        }, 10000);
    });

    $(document).ready(function() {
        // var host = window.location.hostname
        // var ws = new WebSocket("ws://"+host+":8000/humanecho");
        // //document.getElementsByTagName("video")[0].setAttribute("src", aa["video"]);
        // ws.onopen = function() {
        //     console.log('Connected');
        // };
        // ws.onmessage = function(e) {
        //     console.log('Received: ' + e.data);
        //     data = e
        //     var vid = JSON.parse(data.data);
        //     console.log(typeof(vid),vid)
        //     //document.getElementsByTagName("video")[0].setAttribute("src", vid["video"]);
        // };
        // ws.onclose = function(e) {
        //     console.log('Closed');
        // };

	  $('#echo-form').on('submit', function(e) {
      e.preventDefault();
      var message = $('#message').val();

      // 检查是否是打断命令
      if (message.includes('停止') || message.includes('打断') || message.includes('别说了') || message.toLowerCase().includes('stop')) {
          console.log('🛑 检测到文本打断命令: ' + message);
          interruptAI();
          $('#message').val('');
          return;
      }

      console.log('📤 发送消息: ' + message);
      console.log('🆔 sessionid: ', document.getElementById('sessionid').value);

      // 发送消息前先更新状态
      updateStatus('📤 发送中...', '#4299e1');

      fetch('/human', {
            body: JSON.stringify({
                text: message,
                type: 'echo',
                interrupt: true,
                sessionid: parseInt(document.getElementById('sessionid').value),
            }),
            headers: {
                'Content-Type': 'application/json'
            },
            method: 'POST'
      }).then(function(response) {
          if (response.ok) {
              console.log('✅ 消息发送成功');
              updateStatus('✅ 消息已发送', 'green');
              setTimeout(() => {
                  updateStatus('🟢 就绪', 'black', 'linear-gradient(135deg, #f0f0f0, #e0e0e0)', '#ccc');
              }, 2000);
          } else {
              console.error('❌ 消息发送失败');
              updateStatus('❌ 发送失败', 'red');
          }
      }).catch(function(error) {
          console.error('⚠️ 发送错误:', error);
          updateStatus('⚠️ 发送错误', 'red');
      });

      $('#message').val('');
	  });

    $('#btn_start_record').click(function() {
        // 开始录制
        console.log('📹 开始录制...');
        updateStatus('📹 开始录制...', '#4299e1');

        fetch('/record', {
            body: JSON.stringify({
                    type: 'start_record',
                }),
                headers: {
                    'Content-Type': 'application/json'
                },
            method: 'POST'
        }).then(function(response) {
            if (response.ok) {
                console.log('✅ 录制已开始');
                updateStatus('🔴 录制中...', 'red');
                $('#btn_start_record').prop('disabled', true);
                $('#btn_stop_record').prop('disabled', false);
            } else {
                console.error('❌ 录制开始失败');
                updateStatus('❌ 录制失败', 'red');
            }
        }).catch(function(error) {
            console.error('⚠️ 录制错误:', error);
            updateStatus('⚠️ 录制错误', 'red');
        });
    });

    $('#btn_stop_record').click(function() {
        // 结束录制
        console.log('⏹️ 停止录制...');
        updateStatus('⏹️ 停止录制...', '#4299e1');

        fetch('/record', {
            body: JSON.stringify({
                    type: 'end_record',
                }),
                headers: {
                    'Content-Type': 'application/json'
                },
            method: 'POST'
        }).then(function(response) {
            if (response.ok) {
                console.log('✅ 录制已停止');
                updateStatus('✅ 录制完成', 'green');
                $('#btn_start_record').prop('disabled', false);
                $('#btn_stop_record').prop('disabled', true);

                // 2秒后恢复就绪状态
                setTimeout(() => {
                    updateStatus('🟢 就绪', 'black', 'linear-gradient(135deg, #f0f0f0, #e0e0e0)', '#ccc');
                }, 2000);
            } else {
                console.error('❌ 录制停止失败');
                updateStatus('❌ 停止失败', 'red');
            }
        }).catch(function(error) {
            console.error('⚠️ 停止录制错误:', error);
            updateStatus('⚠️ 停止错误', 'red');
        });
    });

    // $('#btn_download').click(function() {
    //     // 下载视频文件
    //     console.log('Downloading video...');
    //     fetch('/record_lasted.mp4', {
    //         method: 'GET'
    //     }).then(function(response) {
    //         if (response.ok) {
    //             return response.blob();
    //         } else {
    //             throw new Error('Failed to download the video.');
    //         }
    //     }).then(function(blob) {
    //         // 创建一个 Blob 对象
    //         const url = window.URL.createObjectURL(blob);
    //         // 创建一个隐藏的可下载链接
    //         const a = document.createElement('a');
    //         a.style.display = 'none';
    //         a.href = url;
    //         a.download = 'record_lasted.mp4';
    //         document.body.appendChild(a);
    //         // 触发下载
    //         a.click();
    //         // 清理
    //         window.URL.revokeObjectURL(url);
    //         document.body.removeChild(a);
    //         console.log('Video downloaded successfully.');
    //     }).catch(function(error) {
    //         console.error('Error:', error);
    //     });
    // });

    }); // 结束 $(document).ready

</script>
</html>
