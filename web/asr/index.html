<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width,initial-scale=1" />
		<title>语音识别</title>
 
	</head>
	<body style="margin-left: 3%">
		<script src="recorder-core.js" charset="UTF-8"></script>
		<script src="wav.js" charset="UTF-8"></script>
		<script src="pcm.js" charset="UTF-8"></script>

		 
		<div class="div_class_topArea">

			<div class="div_class_recordControl">
				asr服务器地址(必填):
				<br>
				<input id="wssip" type="text" onchange="addresschange()" style=" width: 100%;height:100%" value="wss://www.funasr.com:10096/"/>
				<br>
				<a id="wsslink"  href="#" onclick="window.open('https://127.0.0.1:10095/', '_blank')"><div id="info_wslink">点此处手工授权wss://127.0.0.1:10095/</div></a>
				<br>
			<br>  
			<div  style="border:2px solid #ccc;">
				选择录音模式:<br/>
    
        <label><input name="recoder_mode" onclick="on_recoder_mode_change()" type="radio" value="mic" checked="true"/>麦克风 </label>&nbsp;&nbsp;
        <label><input name="recoder_mode" onclick="on_recoder_mode_change()" type="radio" value="file" />文件 </label> 

				</div>
				
				<br>
				 <div id="mic_mode_div" style="border:2px solid #ccc;display:block;">
				选择asr模型模式:<br/>
    
      <label><input name="asr_mode" type="radio" value="2pass" checked="true"/>2pass </label>&nbsp;&nbsp;
      <label><input name="asr_mode" type="radio" value="online" />online </label>&nbsp;&nbsp;
      <label><input name="asr_mode" type="radio" value="offline" />offline </label>

				</div>
				
				<div id="rec_mode_div" style="border:2px solid #ccc;display:none;">
		 
    
		          <input type="file" id="upfile">

				</div>
				<br>
				<div id="use_itn_div" style="border:2px solid #ccc;display:block;">
					逆文本标准化(ITN):<br/>
					<label><input name="use_itn" type="radio" value="false" checked="true"/>否 </label>&nbsp;&nbsp;
					<label><input name="use_itn" type="radio" value="true" />是 </label>
			   </div>
			   <br>
		        <div  style="border:2px solid #ccc;">
					热词设置(一行一个关键字，空格隔开权重,如"阿里巴巴 20")：
					<br>
	
	
					<textarea rows="3"  id="varHot"  style=" width: 100%;height:100%" >阿里巴巴 20&#13;hello world 40</textarea>
					<br>
	
					</div>
				语音识别结果显示：
				<br>
				
				<textarea rows="10"  id="varArea" readonly="true" style=" width: 100%;height:100%" ></textarea>
				<br>
                <div id="info_div">请点击开始</div>
				<div class="div_class_buttons">
					<button id="btnConnect" onclick="start()">连接</button>
					<button id="btnStart" onclick="record()" disabled>开始</button>
					<button id="btnStop" onclick="stop()" disabled>停止</button>
					<!-- 添加专门的打断按钮 -->
					<button id="btnInterrupt" onclick="interruptDirectly()" style="background-color: red; color: white; font-weight: bold;">紧急打断</button>
 
				</div>
                
				<audio id="audio_record" type="audio/wav" controls style="margin-top: 12px; width: 100%;"></audio>
			</div>
		</div>

 		<script src="wsconnecter.js" charset="utf-8"></script>
		<script src="main.js" charset="utf-8"></script>
		<script>
		// 添加直接打断函数
		function interruptDirectly() {
			console.log('发送专门的打断请求...');
			fetch('/interrupt', {
				body: JSON.stringify({
					sessionid: parseInt(parent.document.getElementById('sessionid').value || '0'),
				}),
				headers: {
					'Content-Type': 'application/json'
				},
				method: 'POST'
			}).then(response => response.json())
			  .then(data => {
				  console.log('打断响应:', data);
				  alert('已发送打断请求');
			  })
			  .catch(error => {
				  console.error('打断请求错误:', error);
				  alert('打断请求失败: ' + error);
			  });
		}
		</script>
		
 
				
	</body>
</html>
