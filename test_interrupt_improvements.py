#!/usr/bin/env python3
"""
测试语音打断功能改进的脚本
"""

import time
import threading
import numpy as np
from basereal import BaseReal
from ttsreal import EdgeTTS, State

class MockOpt:
    """模拟配置对象"""
    def __init__(self):
        self.fps = 50  # 50 FPS
        self.tts = "edgetts"
        self.sessionid = "test_session"
        self.customopt = []

class InterruptTester:
    """打断功能测试器"""
    
    def __init__(self):
        self.opt = MockOpt()
        self.base_real = BaseReal(self.opt)
        self.test_results = []
    
    def test_immediate_stop(self):
        """测试立即停止功能"""
        print("=" * 50)
        print("测试立即停止功能")
        print("=" * 50)
        
        # 模拟开始TTS处理
        self.base_real.speaking = True
        
        # 记录开始时间
        start_time = time.time()
        
        # 调用立即停止
        self.base_real.immediate_stop_audio()
        
        # 记录停止时间
        stop_time = time.time()
        
        # 计算响应时间
        response_time = (stop_time - start_time) * 1000  # 转换为毫秒
        
        print(f"立即停止响应时间: {response_time:.2f}ms")
        
        # 检查打断标志是否设置
        if hasattr(self.base_real, 'interrupt_audio') and self.base_real.interrupt_audio:
            print("✅ 打断标志已正确设置")
        else:
            print("❌ 打断标志未设置")
        
        self.test_results.append({
            'test': 'immediate_stop',
            'response_time_ms': response_time,
            'interrupt_flag_set': self.base_real.interrupt_audio
        })
    
    def test_buffer_clearing(self):
        """测试缓冲区清空功能"""
        print("=" * 50)
        print("测试缓冲区清空功能")
        print("=" * 50)
        
        # 模拟向缓冲区添加数据
        if hasattr(self.base_real, 'asr') and hasattr(self.base_real.asr, 'queue'):
            # 添加一些测试数据到队列
            for i in range(10):
                test_audio = np.random.random(320).astype(np.float32)
                self.base_real.asr.queue.put((test_audio, None))
            
            queue_size_before = self.base_real.asr.queue.qsize()
            print(f"清空前队列大小: {queue_size_before}")
            
            # 调用缓冲区清空
            self.base_real._clear_audio_buffers()
            
            queue_size_after = self.base_real.asr.queue.qsize()
            print(f"清空后队列大小: {queue_size_after}")
            
            if queue_size_after == 0:
                print("✅ 缓冲区已成功清空")
            else:
                print("❌ 缓冲区清空失败")
            
            self.test_results.append({
                'test': 'buffer_clearing',
                'queue_size_before': queue_size_before,
                'queue_size_after': queue_size_after,
                'cleared_successfully': queue_size_after == 0
            })
        else:
            print("⚠️  ASR队列不存在，跳过测试")
    
    def test_mini_chunk_processing(self):
        """测试小片段处理功能"""
        print("=" * 50)
        print("测试小片段处理功能")
        print("=" * 50)
        
        # 模拟音频数据
        sample_rate = 16000
        chunk_size = sample_rate // 50  # 20ms chunk
        mini_chunk_size = chunk_size // 2  # 10ms mini chunk
        
        print(f"标准chunk大小: {chunk_size} samples (20ms)")
        print(f"小chunk大小: {mini_chunk_size} samples (10ms)")
        
        # 计算理论响应时间改进
        original_latency = 20  # 20ms
        improved_latency = 10  # 10ms
        improvement_percent = ((original_latency - improved_latency) / original_latency) * 100
        
        print(f"理论延迟改进: {improvement_percent:.1f}% (从{original_latency}ms到{improved_latency}ms)")
        
        self.test_results.append({
            'test': 'mini_chunk_processing',
            'original_chunk_ms': original_latency,
            'mini_chunk_ms': improved_latency,
            'improvement_percent': improvement_percent
        })
    
    def test_interrupt_detection_speed(self):
        """测试打断检测速度"""
        print("=" * 50)
        print("测试打断检测速度")
        print("=" * 50)
        
        # 模拟关键词检测
        keywords = ["停止", "打断", "stop", "interrupt"]
        test_text = "请停止说话"
        
        start_time = time.time()
        
        # 模拟关键词检测过程
        detected = any(keyword in test_text for keyword in keywords)
        
        end_time = time.time()
        
        detection_time = (end_time - start_time) * 1000  # 转换为毫秒
        
        print(f"关键词检测时间: {detection_time:.4f}ms")
        print(f"检测到关键词: {'是' if detected else '否'}")
        
        self.test_results.append({
            'test': 'interrupt_detection_speed',
            'detection_time_ms': detection_time,
            'keyword_detected': detected
        })
    
    def test_check_interval_improvement(self):
        """测试检查间隔改进"""
        print("=" * 50)
        print("测试检查间隔改进")
        print("=" * 50)
        
        original_interval = 0.5  # 原来的500ms
        improved_interval = 0.1  # 改进后的100ms
        
        improvement_factor = original_interval / improved_interval
        
        print(f"原检查间隔: {original_interval * 1000}ms")
        print(f"改进后检查间隔: {improved_interval * 1000}ms")
        print(f"响应速度提升: {improvement_factor}倍")
        
        self.test_results.append({
            'test': 'check_interval_improvement',
            'original_interval_ms': original_interval * 1000,
            'improved_interval_ms': improved_interval * 1000,
            'improvement_factor': improvement_factor
        })
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始语音打断功能改进测试")
        print("=" * 60)
        
        self.test_immediate_stop()
        self.test_buffer_clearing()
        self.test_mini_chunk_processing()
        self.test_interrupt_detection_speed()
        self.test_check_interval_improvement()
        
        self.print_summary()
    
    def print_summary(self):
        """打印测试总结"""
        print("=" * 60)
        print("📊 测试结果总结")
        print("=" * 60)
        
        for result in self.test_results:
            test_name = result['test']
            print(f"\n🔍 {test_name}:")
            
            for key, value in result.items():
                if key != 'test':
                    print(f"  {key}: {value}")
        
        print("\n" + "=" * 60)
        print("✅ 所有测试完成")
        
        # 计算总体改进
        total_improvements = []
        
        for result in self.test_results:
            if 'improvement_percent' in result:
                total_improvements.append(result['improvement_percent'])
            elif 'improvement_factor' in result:
                total_improvements.append((result['improvement_factor'] - 1) * 100)
        
        if total_improvements:
            avg_improvement = sum(total_improvements) / len(total_improvements)
            print(f"📈 平均性能改进: {avg_improvement:.1f}%")

def main():
    """主函数"""
    tester = InterruptTester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()
