###############################################################################
#  Copyright (C) 2024 LiveTalking@lipku https://github.com/lipku/LiveTalking
#  email: <EMAIL>
# 
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#  
#       http://www.apache.org/licenses/LICENSE-2.0
# 
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
###############################################################################

import math
import torch
import numpy as np

import subprocess
import os
import time
import cv2
import glob
import resampy
import threading

import queue
from queue import Queue
from threading import Thread, Event
from io import BytesIO
import soundfile as sf

import av
from fractions import Fraction

from ttsreal import EdgeTTS,SovitsTTS,XTTS,CosyVoiceTTS,FishTTS,TencentTTS
from logger import logger

from tqdm import tqdm
def read_imgs(img_list):
    frames = []
    logger.info('reading images...')
    for img_path in tqdm(img_list):
        frame = cv2.imread(img_path)
        frames.append(frame)
    return frames

class BaseReal:
    def __init__(self, opt):
        self.opt = opt
        self.sample_rate = 16000
        self.chunk = self.sample_rate // opt.fps # 320 samples per chunk (20ms * 16000 / 1000)
        self.sessionid = self.opt.sessionid

        if opt.tts == "edgetts":
            self.tts = EdgeTTS(opt,self)
        elif opt.tts == "gpt-sovits":
            self.tts = SovitsTTS(opt,self)
        elif opt.tts == "xtts":
            self.tts = XTTS(opt,self)
        elif opt.tts == "cosyvoice":
            self.tts = CosyVoiceTTS(opt,self)
        elif opt.tts == "fishtts":
            self.tts = FishTTS(opt,self)
        elif opt.tts == "tencent":
            self.tts = TencentTTS(opt,self)
        
        self.speaking = False

        self.interrupt_audio = False    # woody4interrput

        self.recording = False
        self._record_video_pipe = None
        self._record_audio_pipe = None
        self.width = self.height = 0

        self.curr_state=0
        self.custom_img_cycle = {}
        self.custom_audio_cycle = {}
        self.custom_audio_index = {}
        self.custom_index = {}
        self.custom_opt = {}
        self.__loadcustom()

        # 添加打断检测线程
        self.interrupt_check_thread = threading.Thread(target=self.check_interrupt)
        self.interrupt_check_thread.daemon = True
        self.interrupt_check_thread.start()

    def put_msg_txt(self, msg, eventpoint=None):
        """处理文本消息，确保在开始新的讲故事任务前正确重置状态"""
        # 记录当前状态
        was_interrupted = False
        if hasattr(self, 'interrupt_audio'):
            was_interrupted = self.interrupt_audio
        
        # 重置打断标志（只在开始新的讲故事任务时）
        if was_interrupted:
            print(f"[RESET] 开始新的讲故事任务，重置打断标志，时间：{time.strftime('%H:%M:%S')}")
            self.interrupt_audio = False
        
        # 发送消息到TTS
        if len(msg) > 0:
            self.tts.put_msg_txt(msg, eventpoint)
    
    def put_audio_frame(self, audio_chunk, eventpoint=None):
        """处理音频帧，每次处理前检查打断标志"""
        # 检查打断状态
        if hasattr(self, 'interrupt_audio') and self.interrupt_audio:
            print(f"[INTERRUPT] put_audio_frame检测到打断标志为True，跳过处理，时间：{time.strftime('%H:%M:%S')}")
            # 如果是结束事件，可能需要特殊处理
            if eventpoint and eventpoint.get('status') == 'end':
                print(f"[INTERRUPT] 检测到结束事件，但由于打断标志为True，跳过处理，时间：{time.strftime('%H:%M:%S')}")

            # 立即清空相关缓冲区
            self._clear_audio_buffers()
            return

        # 如果有eventpoint，记录日志
        if eventpoint:
            status = eventpoint.get('status')
            text = eventpoint.get('text', '')[:30]  # 只显示前30个字符
            print(f"[AUDIO] 处理音频帧，状态：{status}，文本：{text}...，时间：{time.strftime('%H:%M:%S')}")

        # 处理音频帧
        if hasattr(self, 'asr') and hasattr(self.asr, 'put_audio_frame'):
            self.asr.put_audio_frame(audio_chunk, eventpoint)
        else:
            print(f"[ERROR] 无法处理音频帧，ASR对象不存在或没有put_audio_frame方法，时间：{time.strftime('%H:%M:%S')}")

    def _clear_audio_buffers(self):
        """清空所有音频相关的缓冲区"""
        try:
            # 清空ASR队列
            if hasattr(self, 'asr'):
                if hasattr(self.asr, 'queue'):
                    while not self.asr.queue.empty():
                        try:
                            self.asr.queue.get_nowait()
                        except:
                            pass

                if hasattr(self.asr, 'output_queue'):
                    while not self.asr.output_queue.empty():
                        try:
                            self.asr.output_queue.get_nowait()
                        except:
                            pass

            print(f"[CLEAR] 已清空音频缓冲区，时间：{time.strftime('%H:%M:%S')}")
        except Exception as e:
            print(f"[ERROR] 清空音频缓冲区时出错：{str(e)}，时间：{time.strftime('%H:%M:%S')}")

    def put_audio_file(self,filebyte): 
        self.interrupt_audio = False    # woody4interrupt
        input_stream = BytesIO(filebyte)
        stream = self.__create_bytes_stream(input_stream)
        streamlen = stream.shape[0]
        idx=0
        while streamlen >= self.chunk:  #and self.state==State.RUNNING
            self.put_audio_frame(stream[idx:idx+self.chunk])
            streamlen -= self.chunk
            idx += self.chunk
    
    def __create_bytes_stream(self,byte_stream):
        #byte_stream=BytesIO(buffer)
        stream, sample_rate = sf.read(byte_stream) # [T*sample_rate,] float64
        logger.info(f'[INFO]put audio stream {sample_rate}: {stream.shape}')
        stream = stream.astype(np.float32)

        if stream.ndim > 1:
            logger.info(f'[WARN] audio has {stream.shape[1]} channels, only use the first.')
            stream = stream[:, 0]
    
        if sample_rate != self.sample_rate and stream.shape[0]>0:
            logger.info(f'[WARN] audio sample rate is {sample_rate}, resampling into {self.sample_rate}.')
            stream = resampy.resample(x=stream, sr_orig=sample_rate, sr_new=self.sample_rate)

        return stream

    def flush_talk(self):
        """完全停止所有处理，确保打断功能正常工作"""
        print(f"[FLUSH] 调用flush_talk方法，时间：{time.strftime('%H:%M:%S')}")

        # 设置打断标志
        self.interrupt_audio = True
        print(f"[FLUSH] 已设置interrupt_audio=True，时间：{time.strftime('%H:%M:%S')}")

        # 停止TTS处理
        if hasattr(self, 'tts'):
            # 清空TTS消息队列
            if hasattr(self.tts, 'msgqueue'):
                try:
                    print(f"[FLUSH] 开始清空TTS消息队列，时间：{time.strftime('%H:%M:%S')}")

                    # 使用循环确保队列被完全清空
                    max_attempts = 5
                    for attempt in range(max_attempts):
                        if self.tts.msgqueue.empty():
                            break

                        try:
                            while not self.tts.msgqueue.empty():
                                self.tts.msgqueue.get_nowait()
                        except Exception as e:
                            print(f"[FLUSH] 清空队列时出错: {e}，时间：{time.strftime('%H:%M:%S')}")
                            break

                        time.sleep(0.01)

                    print(f"[FLUSH] 已清空TTS消息队列，尝试次数：{attempt+1}，时间：{time.strftime('%H:%M:%S')}")
                except Exception as e:
                    print(f"[FLUSH] 清空TTS消息队列时出错：{str(e)}，时间：{time.strftime('%H:%M:%S')}")

            # 清空TTS输入流缓冲区
            if hasattr(self.tts, 'input_stream'):
                try:
                    self.tts.input_stream.seek(0)
                    self.tts.input_stream.truncate()
                    print(f"[FLUSH] 已清空TTS输入流缓冲区，时间：{time.strftime('%H:%M:%S')}")
                except Exception as e:
                    print(f"[FLUSH] 清空TTS输入流时出错：{str(e)}，时间：{time.strftime('%H:%M:%S')}")

        # 清空ASR相关队列
        if hasattr(self, 'asr'):
            try:
                # 清空ASR输入队列
                if hasattr(self.asr, 'queue'):
                    try:
                        while not self.asr.queue.empty():
                            try:
                                self.asr.queue.get_nowait()
                            except:
                                pass
                        print(f"[FLUSH] 已清空ASR输入队列，时间：{time.strftime('%H:%M:%S')}")
                    except Exception as e:
                        print(f"[FLUSH] 清空ASR输入队列时出错：{str(e)}，时间：{time.strftime('%H:%M:%S')}")

                # 清空ASR输出队列
                if hasattr(self.asr, 'output_queue'):
                    try:
                        while not self.asr.output_queue.empty():
                            try:
                                self.asr.output_queue.get_nowait()
                            except:
                                pass
                        print(f"[FLUSH] 已清空ASR输出队列，时间：{time.strftime('%H:%M:%S')}")
                    except Exception as e:
                        print(f"[FLUSH] 清空ASR输出队列时出错：{str(e)}，时间：{time.strftime('%H:%M:%S')}")

                # 清空特征队列
                if hasattr(self.asr, 'feat_queue'):
                    try:
                        while not self.asr.feat_queue.empty():
                            try:
                                self.asr.feat_queue.get_nowait()
                            except:
                                pass
                        print(f"[FLUSH] 已清空ASR特征队列，时间：{time.strftime('%H:%M:%S')}")
                    except Exception as e:
                        print(f"[FLUSH] 清空ASR特征队列时出错：{str(e)}，时间：{time.strftime('%H:%M:%S')}")

            except Exception as e:
                print(f"[FLUSH] 清空ASR队列时出错：{str(e)}，时间：{time.strftime('%H:%M:%S')}")

        # 确保打断标志在一段时间内保持为True
        def reset_interrupt_flag():
            time.sleep(0.5)  # 减少等待时间到0.5秒，提高响应速度
            if hasattr(self, 'interrupt_audio'):
                print(f"[FLUSH] 延迟重置interrupt_audio=False，时间：{time.strftime('%H:%M:%S')}")
                self.interrupt_audio = False

        # 启动一个线程来延迟重置打断标志
        threading.Thread(target=reset_interrupt_flag, daemon=True).start()

    def is_speaking(self)->bool:
        return self.speaking
    
    def __loadcustom(self):
        for item in self.opt.customopt:
            logger.info(item)
            input_img_list = glob.glob(os.path.join(item['imgpath'], '*.[jpJP][pnPN]*[gG]'))
            input_img_list = sorted(input_img_list, key=lambda x: int(os.path.splitext(os.path.basename(x))[0]))
            self.custom_img_cycle[item['audiotype']] = read_imgs(input_img_list)
            self.custom_audio_cycle[item['audiotype']], sample_rate = sf.read(item['audiopath'], dtype='float32')
            self.custom_audio_index[item['audiotype']] = 0
            self.custom_index[item['audiotype']] = 0
            self.custom_opt[item['audiotype']] = item

    def init_customindex(self):
        self.curr_state=0
        for key in self.custom_audio_index:
            self.custom_audio_index[key]=0
        for key in self.custom_index:
            self.custom_index[key]=0

    def notify(self,eventpoint):
        logger.info("notify:%s",eventpoint)

    def start_recording(self):
        """开始录制视频"""
        if self.recording:
            return

        command = ['ffmpeg',
                    '-y', '-an',
                    '-f', 'rawvideo',
                    '-vcodec','rawvideo',
                    '-pix_fmt', 'bgr24', #像素格式
                    '-s', "{}x{}".format(self.width, self.height),
                    '-r', str(25),
                    '-i', '-',
                    '-pix_fmt', 'yuv420p', 
                    '-vcodec', "h264",
                    #'-f' , 'flv',                  
                    f'temp{self.opt.sessionid}.mp4']
        self._record_video_pipe = subprocess.Popen(command, shell=False, stdin=subprocess.PIPE)

        acommand = ['ffmpeg',
                    '-y', '-vn',
                    '-f', 's16le',
                    #'-acodec','pcm_s16le',
                    '-ac', '1',
                    '-ar', '16000',
                    '-i', '-',
                    '-acodec', 'aac',
                    #'-f' , 'wav',                  
                    f'temp{self.opt.sessionid}.aac']
        self._record_audio_pipe = subprocess.Popen(acommand, shell=False, stdin=subprocess.PIPE)

        self.recording = True
        # self.recordq_video.queue.clear()
        # self.recordq_audio.queue.clear()
        # self.container = av.open(path, mode="w")
    
        # process_thread = Thread(target=self.record_frame, args=())
        # process_thread.start()
    
    def record_video_data(self,image):
        if self.width == 0:
            print("image.shape:",image.shape)
            self.height,self.width,_ = image.shape
        if self.recording:
            self._record_video_pipe.stdin.write(image.tostring())

    def record_audio_data(self,frame):
        if self.recording:
            self._record_audio_pipe.stdin.write(frame.tostring())
    
    # def record_frame(self): 
    #     videostream = self.container.add_stream("libx264", rate=25)
    #     videostream.codec_context.time_base = Fraction(1, 25)
    #     audiostream = self.container.add_stream("aac")
    #     audiostream.codec_context.time_base = Fraction(1, 16000)
    #     init = True
    #     framenum = 0       
    #     while self.recording:
    #         try:
    #             videoframe = self.recordq_video.get(block=True, timeout=1)
    #             videoframe.pts = framenum #int(round(framenum*0.04 / videostream.codec_context.time_base))
    #             videoframe.dts = videoframe.pts
    #             if init:
    #                 videostream.width = videoframe.width
    #                 videostream.height = videoframe.height
    #                 init = False
    #             for packet in videostream.encode(videoframe):
    #                 self.container.mux(packet)
    #             for k in range(2):
    #                 audioframe = self.recordq_audio.get(block=True, timeout=1)
    #                 audioframe.pts = int(round((framenum*2+k)*0.02 / audiostream.codec_context.time_base))
    #                 audioframe.dts = audioframe.pts
    #                 for packet in audiostream.encode(audioframe):
    #                     self.container.mux(packet)
    #             framenum += 1
    #         except queue.Empty:
    #             print('record queue empty,')
    #             continue
    #         except Exception as e:
    #             print(e)
    #             #break
    #     for packet in videostream.encode(None):
    #         self.container.mux(packet)
    #     for packet in audiostream.encode(None):
    #         self.container.mux(packet)
    #     self.container.close()
    #     self.recordq_video.queue.clear()
    #     self.recordq_audio.queue.clear()
    #     print('record thread stop')
		
    def stop_recording(self):
        """停止录制视频"""
        if not self.recording:
            return
        self.recording = False 
        self._record_video_pipe.stdin.close()  #wait() 
        self._record_video_pipe.wait()
        self._record_audio_pipe.stdin.close()
        self._record_audio_pipe.wait()
        cmd_combine_audio = f"ffmpeg -y -i temp{self.opt.sessionid}.aac -i temp{self.opt.sessionid}.mp4 -c:v copy -c:a copy data/record.mp4"
        os.system(cmd_combine_audio) 
        #os.remove(output_path)

    def mirror_index(self,size, index):
        #size = len(self.coord_list_cycle)
        turn = index // size
        res = index % size
        if turn % 2 == 0:
            return res
        else:
            return size - res - 1 
    
    def get_audio_stream(self,audiotype):
        idx = self.custom_audio_index[audiotype]
        stream = self.custom_audio_cycle[audiotype][idx:idx+self.chunk]
        self.custom_audio_index[audiotype] += self.chunk
        if self.custom_audio_index[audiotype]>=self.custom_audio_cycle[audiotype].shape[0]:
            self.curr_state = 1  #当前视频不循环播放，切换到静音状态
        return stream
    
    def set_curr_state(self,audiotype, reinit):
        print('set_curr_state:',audiotype)
        self.curr_state = audiotype
        if reinit:
            self.custom_audio_index[audiotype] = 0
            self.custom_index[audiotype] = 0
    
    # def process_custom(self,audiotype:int,idx:int):
    #     if self.curr_state!=audiotype: #从推理切到口播
    #         if idx in self.switch_pos:  #在卡点位置可以切换
    #             self.curr_state=audiotype
    #             self.custom_index=0
    #     else:
    #         self.custom_index+=1

    def immediate_stop_audio(self):
        """立即停止音频播放，清空所有缓冲区"""
        print(f"[IMMEDIATE_STOP] 立即停止音频播放，时间：{time.strftime('%H:%M:%S')}")

        # 设置打断标志
        self.interrupt_audio = True

        # 立即清空所有缓冲区
        self._clear_audio_buffers()

        # 停止TTS处理
        if hasattr(self, 'tts'):
            # 设置TTS状态为暂停
            if hasattr(self.tts, 'state'):
                from ttsreal import State
                self.tts.state = State.PAUSE
                print(f"[IMMEDIATE_STOP] 已设置TTS状态为PAUSE，时间：{time.strftime('%H:%M:%S')}")

            # 清空TTS输入流
            if hasattr(self.tts, 'input_stream'):
                try:
                    self.tts.input_stream.seek(0)
                    self.tts.input_stream.truncate()
                    print(f"[IMMEDIATE_STOP] 已清空TTS输入流，时间：{time.strftime('%H:%M:%S')}")
                except Exception as e:
                    print(f"[IMMEDIATE_STOP] 清空TTS输入流时出错：{str(e)}，时间：{time.strftime('%H:%M:%S')}")

        print(f"[IMMEDIATE_STOP] 立即停止完成，时间：{time.strftime('%H:%M:%S')}")

    def check_interrupt(self):
        """定期检查打断标志和用户输入"""
        while True:
            # 检查是否有用户输入表示打断
            # 这里可以添加检查麦克风输入或其他打断信号的逻辑

            # 如果检测到打断信号，设置打断标志
            # self.interrupt_audio = True
            # self.flush_talk()

            # 打印当前状态（仅用于调试）
            if hasattr(self, 'speaking') and self.speaking:
                print(f"AI正在说话，interrupt_audio={self.interrupt_audio if hasattr(self, 'interrupt_audio') else 'unknown'}，时间：{time.strftime('%H:%M:%S')}")

            # 减少检查间隔，提高响应速度
            time.sleep(0.1)  # 从0.5秒减少到0.1秒

    def force_interrupt(self):
        """强制打断当前处理，包括清空所有队列和停止所有处理"""
        print(f"[FORCE_INTERRUPT] 开始强制打断，时间：{time.strftime('%H:%M:%S')}")
        
        # 设置打断标志
        self.interrupt_audio = True
        
        # 停止TTS处理
        if hasattr(self, 'tts'):
            # 清空TTS消息队列
            if hasattr(self.tts, 'msgqueue'):
                try:
                    while not self.tts.msgqueue.empty():
                        try:
                            self.tts.msgqueue.get_nowait()
                        except:
                            pass
                    print(f"[FORCE_INTERRUPT] 已清空TTS消息队列，时间：{time.strftime('%H:%M:%S')}")
                except:
                    print(f"[FORCE_INTERRUPT] 清空TTS消息队列时出错，时间：{time.strftime('%H:%M:%S')}")
            
            # 重置TTS状态
            if hasattr(self.tts, 'state'):
                try:
                    from enum import Enum
                    if isinstance(self.tts.state, Enum):
                        self.tts.state = next((s for s in type(self.tts.state) if s.name == 'IDLE'), self.tts.state)
                    else:
                        self.tts.state = 0  # 假设0代表IDLE状态
                    print(f"[FORCE_INTERRUPT] 已重置TTS状态，时间：{time.strftime('%H:%M:%S')}")
                except:
                    print(f"[FORCE_INTERRUPT] 重置TTS状态时出错，时间：{time.strftime('%H:%M:%S')}")
            
            # 如果有input_stream，清空它
            if hasattr(self.tts, 'input_stream'):
                try:
                    self.tts.input_stream.seek(0)
                    self.tts.input_stream.truncate()
                    print(f"[FORCE_INTERRUPT] 已清空TTS输入流，时间：{time.strftime('%H:%M:%S')}")
                except:
                    print(f"[FORCE_INTERRUPT] 清空TTS输入流时出错，时间：{time.strftime('%H:%M:%S')}")
        
        # 如果有ASR，重置它
        if hasattr(self, 'asr'):
            try:
                # 清空ASR队列
                if hasattr(self.asr, 'output_queue'):
                    try:
                        while not self.asr.output_queue.empty():
                            try:
                                self.asr.output_queue.get_nowait()
                            except:
                                pass
                        print(f"[FORCE_INTERRUPT] 已清空ASR输出队列，时间：{time.strftime('%H:%M:%S')}")
                    except:
                        print(f"[FORCE_INTERRUPT] 清空ASR输出队列时出错，时间：{time.strftime('%H:%M:%S')}")
                
                # 重置ASR状态
                if hasattr(self.asr, 'reset'):
                    self.asr.reset()
                    print(f"[FORCE_INTERRUPT] 已重置ASR状态，时间：{time.strftime('%H:%M:%S')}")
            except:
                print(f"[FORCE_INTERRUPT] 重置ASR时出错，时间：{time.strftime('%H:%M:%S')}")
        
        print(f"[FORCE_INTERRUPT] 强制打断完成，时间：{time.strftime('%H:%M:%S')}")
