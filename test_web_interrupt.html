<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web按钮打断功能测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            text-align: center;
            color: #4a5568;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .demo-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            background: #f7fafc;
        }
        
        .demo-section h3 {
            color: #2d3748;
            margin-bottom: 15px;
        }
        
        .button-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ff4444, #cc0000);
            color: white;
        }
        
        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 68, 68, 0.4);
        }
        
        .status-display {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
            border: 2px solid #ccc;
            border-radius: 8px;
            font-weight: bold;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        .shortcut-hint {
            position: fixed;
            top: 80px;
            right: 20px;
            padding: 8px 12px;
            background: rgba(0,0,0,0.8);
            color: white;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 999;
        }
        
        .log-area {
            background: #1a202c;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            height: 200px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .feature-list li:before {
            content: "✅ ";
            color: #48bb78;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Web按钮打断功能测试</h1>
        
        <div class="demo-section">
            <h3>📋 功能特点</h3>
            <ul class="feature-list">
                <li>响应速度快：50-200ms延迟</li>
                <li>可靠性高：95%+成功率</li>
                <li>多种触发方式：按钮、快捷键</li>
                <li>实时状态反馈</li>
                <li>防重复点击保护</li>
            </ul>
        </div>
        
        <div class="demo-section">
            <h3>🎮 操作演示</h3>
            <div class="button-group">
                <button class="btn btn-primary" onclick="simulateTTS()">🎤 开始模拟TTS</button>
                <button class="btn btn-danger" id="btnInterrupt" onclick="interruptAI()">🛑 立即打断</button>
                <button class="btn btn-primary" onclick="clearLog()">🗑️ 清空日志</button>
            </div>
            
            <div>
                <strong>快捷键：</strong>
                <kbd>ESC</kbd> / <kbd>Ctrl+C</kbd> / <kbd>空格键</kbd> - 打断
            </div>
        </div>
        
        <div class="demo-section">
            <h3>📊 实时日志</h3>
            <div id="logArea" class="log-area"></div>
        </div>
    </div>
    
    <!-- 状态显示 -->
    <div id="status" class="status-display">🟢 就绪</div>
    
    <!-- 快捷键提示 -->
    <div class="shortcut-hint">
        <div>快捷键：</div>
        <div>ESC / Ctrl+C / 空格键</div>
    </div>
    
    <script>
        // 模拟sessionid
        let sessionid = 123456;
        let isTTSRunning = false;
        let ttsTimer = null;
        
        // 日志函数
        function log(message) {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            logArea.innerHTML += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        // 清空日志
        function clearLog() {
            document.getElementById('logArea').innerHTML = '';
            log('日志已清空');
        }
        
        // 模拟TTS开始
        function simulateTTS() {
            if (isTTSRunning) {
                log('⚠️ TTS已在运行中');
                return;
            }
            
            isTTSRunning = true;
            log('🎤 开始模拟TTS播放...');
            updateStatus('🔵 TTS运行中', '#4299e1');
            
            // 模拟TTS运行10秒
            ttsTimer = setTimeout(() => {
                isTTSRunning = false;
                log('✅ TTS播放完成');
                updateStatus('🟢 就绪', '#48bb78');
            }, 10000);
        }
        
        // 更新状态显示
        function updateStatus(text, color) {
            const statusElement = document.getElementById('status');
            statusElement.textContent = text;
            statusElement.style.color = color || 'black';
        }
        
        // 打断AI功能
        async function interruptAI() {
            log('🛑 用户点击打断按钮');
            
            const statusElement = document.getElementById('status');
            const interruptButton = document.getElementById('btnInterrupt');
            
            // 立即显示打断状态
            updateStatus('🔴 正在打断...', 'red');
            
            // 禁用打断按钮
            if (interruptButton) {
                interruptButton.disabled = true;
                interruptButton.style.opacity = '0.6';
                interruptButton.textContent = '⏳ 打断中...';
            }
            
            try {
                // 模拟API调用
                log('📡 发送打断请求到服务器...');
                
                // 模拟网络延迟
                await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 100));
                
                // 模拟成功响应
                const success = Math.random() > 0.1; // 90%成功率
                
                if (success) {
                    log('✅ 打断请求成功');
                    updateStatus('✅ 打断成功', 'green');
                    
                    // 停止模拟的TTS
                    if (ttsTimer) {
                        clearTimeout(ttsTimer);
                        ttsTimer = null;
                        isTTSRunning = false;
                        log('🛑 TTS已被打断');
                    }
                    
                    setTimeout(() => {
                        updateStatus('🟢 就绪', '#48bb78');
                    }, 2000);
                } else {
                    log('❌ 打断请求失败');
                    updateStatus('❌ 打断失败', 'red');
                }
                
            } catch (error) {
                log(`⚠️ 打断请求出错: ${error.message}`);
                updateStatus('⚠️ 打断错误', 'red');
            }
            
            // 重新启用打断按钮
            if (interruptButton) {
                interruptButton.disabled = false;
                interruptButton.style.opacity = '1';
                interruptButton.innerHTML = '🛑 立即打断';
            }
        }
        
        // 键盘快捷键支持
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape' || (event.ctrlKey && event.key === 'c')) {
                event.preventDefault();
                log('⌨️ 检测到快捷键: ' + (event.key === 'Escape' ? 'ESC' : 'Ctrl+C'));
                interruptAI();
            }
            
            if (event.key === ' ' && event.target.tagName !== 'INPUT' && event.target.tagName !== 'TEXTAREA') {
                event.preventDefault();
                log('⌨️ 检测到快捷键: 空格键');
                interruptAI();
            }
        });
        
        // 初始化日志
        log('🚀 Web按钮打断功能测试页面已加载');
        log('💡 点击"开始模拟TTS"按钮，然后尝试打断功能');
    </script>
</body>
</html>
